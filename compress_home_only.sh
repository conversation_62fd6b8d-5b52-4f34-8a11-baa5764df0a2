#!/bin/bash

# 🎬 Home 视频专用全方案压缩脚本
# 基于压缩方案选择.md，使用双线程和原分辨率

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 视频信息
INPUT_FILE="home/home.mp4"
VIDEO_NAME="home"
RESOLUTION="2560:1440"
ORIGINAL_SIZE="63M"

# 创建输出目录
create_output_dirs() {
    mkdir -p compressed_home/{h265_crf12,h265_crf14,h265_2pass,h265_hybrid,av1_crf15,vp9_crf18,h264_crf16}
    mkdir -p logs_home
    log_info "创建输出目录完成"
}

# 方案1: H.265 CRF 12 接近无损
compress_h265_crf12() {
    local output_file="compressed_home/h265_crf12/home_h265_crf12.mp4"
    
    log_info "开始 H.265 CRF 12 压缩 (接近无损)"
    
    ffmpeg -i "$INPUT_FILE" \
        -c:v libx265 \
        -crf 12 \
        -preset slower \
        -profile:v main \
        -level 5.1 \
        -x265-params "me=umh:subme=3:ref=5:bframes=8:rd=3:psy-rd=1.0:aq-mode=3" \
        -vf "scale=${RESOLUTION}:flags=lanczos:param0=3" \
        -colorspace bt709 \
        -color_primaries bt709 \
        -color_trc bt709 \
        -an \
        -movflags +faststart \
        -pix_fmt yuv420p \
        -threads 2 \
        -y "$output_file" 2>&1 | tee "logs_home/h265_crf12.log"
    
    if [ $? -eq 0 ]; then
        local size=$(du -h "$output_file" | cut -f1)
        log_success "H.265 CRF 12 完成: $size"
    else
        log_error "H.265 CRF 12 失败"
    fi
}

# 方案2: H.265 CRF 14 推荐方案
compress_h265_crf14() {
    local output_file="compressed_home/h265_crf14/home_h265_crf14.mp4"
    
    log_info "开始 H.265 CRF 14 压缩 (推荐方案)"
    
    ffmpeg -i "$INPUT_FILE" \
        -c:v libx265 \
        -crf 14 \
        -preset slower \
        -profile:v main \
        -level 5.1 \
        -x265-params "me=umh:subme=3:ref=5:bframes=8:b-adapt=2:rd=3:psy-rd=1.0:psy-rdoq=1.0:aq-mode=3:aq-strength=0.8:deblock=1,1" \
        -vf "scale=${RESOLUTION}:flags=lanczos:param0=3" \
        -colorspace bt709 \
        -color_primaries bt709 \
        -color_trc bt709 \
        -an \
        -movflags +faststart \
        -pix_fmt yuv420p \
        -threads 2 \
        -y "$output_file" 2>&1 | tee "logs_home/h265_crf14.log"
    
    if [ $? -eq 0 ]; then
        local size=$(du -h "$output_file" | cut -f1)
        log_success "H.265 CRF 14 完成: $size"
    else
        log_error "H.265 CRF 14 失败"
    fi
}

# 方案3: H.265 两遍编码
compress_h265_2pass() {
    local output_file="compressed_home/h265_2pass/home_h265_2pass.mp4"
    local bitrate="8000k"  # 适合15秒短视频的码率
    
    log_info "开始 H.265 两遍编码 (码率: $bitrate)"
    
    # 第一遍
    log_info "第一遍分析..."
    ffmpeg -i "$INPUT_FILE" \
        -c:v libx265 \
        -b:v "$bitrate" \
        -preset slower \
        -pass 1 \
        -threads 2 \
        -f null /dev/null 2>&1 | tee "logs_home/h265_2pass_pass1.log"
    
    if [ $? -eq 0 ]; then
        log_info "第二遍编码..."
        ffmpeg -i "$INPUT_FILE" \
            -c:v libx265 \
            -b:v "$bitrate" \
            -preset slower \
            -pass 2 \
            -profile:v main \
            -level 5.1 \
            -vf "scale=${RESOLUTION}:flags=lanczos" \
            -an \
            -movflags +faststart \
            -threads 2 \
            -y "$output_file" 2>&1 | tee "logs_home/h265_2pass_pass2.log"
        
        if [ $? -eq 0 ]; then
            local size=$(du -h "$output_file" | cut -f1)
            log_success "H.265 两遍编码完成: $size"
            rm -f ffmpeg2pass-*.log
        else
            log_error "H.265 两遍编码第二遍失败"
        fi
    else
        log_error "H.265 两遍编码第一遍失败"
    fi
}

# 方案4: H.265 混合编码策略
compress_h265_hybrid() {
    local output_file="compressed_home/h265_hybrid/home_h265_hybrid.mp4"
    
    log_info "开始 H.265 混合编码 (最佳质量/大小平衡)"
    
    ffmpeg -i "$INPUT_FILE" \
        -c:v libx265 \
        -crf 14 \
        -preset slower \
        -x265-params "me=umh:subme=3:ref=6:bframes=8:b-adapt=2:rd=4:psy-rd=1.0:psy-rdoq=2.0:aq-mode=3:aq-strength=0.8:qcomp=0.6:deblock=1,1:sao=1:limit-sao=1" \
        -vf "hqdn3d=1:0.5:1:0.5,scale=${RESOLUTION}:flags=lanczos:param0=3" \
        -an \
        -threads 2 \
        -y "$output_file" 2>&1 | tee "logs_home/h265_hybrid.log"
    
    if [ $? -eq 0 ]; then
        local size=$(du -h "$output_file" | cut -f1)
        log_success "H.265 混合编码完成: $size"
    else
        log_error "H.265 混合编码失败"
    fi
}

# 方案5: AV1 最新技术
compress_av1() {
    local output_file="compressed_home/av1_crf15/home_av1_crf15.mp4"
    
    log_info "开始 AV1 CRF 15 压缩 (最新技术)"
    
    ffmpeg -i "$INPUT_FILE" \
        -c:v libaom-av1 \
        -crf 15 \
        -cpu-used 1 \
        -row-mt 1 \
        -tiles 4x2 \
        -g 240 \
        -keyint_min 23 \
        -aq-mode 1 \
        -tune-content 0 \
        -vf "scale=${RESOLUTION}:flags=lanczos" \
        -an \
        -threads 2 \
        -y "$output_file" 2>&1 | tee "logs_home/av1_crf15.log"
    
    if [ $? -eq 0 ]; then
        local size=$(du -h "$output_file" | cut -f1)
        log_success "AV1 CRF 15 完成: $size"
    else
        log_error "AV1 CRF 15 失败"
    fi
}

# 方案6: VP9 优化
compress_vp9() {
    local output_file="compressed_home/vp9_crf18/home_vp9_crf18.webm"
    
    log_info "开始 VP9 CRF 18 压缩 (开源方案)"
    
    ffmpeg -i "$INPUT_FILE" \
        -c:v libvpx-vp9 \
        -crf 18 \
        -b:v 0 \
        -cpu-used 2 \
        -row-mt 1 \
        -tile-columns 2 \
        -frame-parallel 1 \
        -vf "scale=${RESOLUTION}:flags=lanczos" \
        -an \
        -threads 2 \
        -y "$output_file" 2>&1 | tee "logs_home/vp9_crf18.log"
    
    if [ $? -eq 0 ]; then
        local size=$(du -h "$output_file" | cut -f1)
        log_success "VP9 CRF 18 完成: $size"
    else
        log_error "VP9 CRF 18 失败"
    fi
}

# 方案7: H.264 传统标准
compress_h264() {
    local output_file="compressed_home/h264_crf16/home_h264_crf16.mp4"

    log_info "开始 H.264 CRF 16 压缩 (传统兼容)"

    ffmpeg -i "$INPUT_FILE" \
        -c:v libx264 \
        -crf 16 \
        -preset veryslow \
        -profile:v high \
        -level 5.1 \
        -vf "scale=${RESOLUTION}:flags=lanczos" \
        -an \
        -movflags +faststart \
        -pix_fmt yuv420p \
        -threads 2 \
        -y "$output_file" 2>&1 | tee "logs_home/h264_crf16.log"

    if [ $? -eq 0 ]; then
        local size=$(du -h "$output_file" | cut -f1)
        log_success "H.264 CRF 16 完成: $size"
    else
        log_error "H.264 CRF 16 失败"
    fi
}

# 生成压缩报告
generate_report() {
    local report_file="compressed_home/HOME_COMPRESSION_REPORT.md"

    log_info "生成压缩报告: $report_file"

    # 获取原始文件信息
    local original_size_bytes=$(stat -c%s "$INPUT_FILE")
    local original_size_mb=$((original_size_bytes / 1024 / 1024))

    cat > "$report_file" << EOF
# 📊 Home 视频压缩报告 - 全方案对比

## 原始文件信息
- **文件名**: $INPUT_FILE
- **文件大小**: ${ORIGINAL_SIZE} (${original_size_mb} MB)
- **分辨率**: 2560x1440
- **时长**: 15.017秒
- **帧率**: ~60fps
- **总帧数**: 899帧
- **压缩时间**: $(date '+%Y-%m-%d %H:%M:%S')

## 压缩结果对比

| 方案 | 编码器 | 参数 | 文件大小 | 压缩比 | 画质等级 | 兼容性 |
|------|--------|------|----------|--------|----------|--------|
EOF

    # 添加各方案的结果
    for method in h265_crf12 h265_crf14 h265_2pass h265_hybrid av1_crf15 h264_crf16; do
        local output_dir="compressed_home/${method}"
        local output_file=$(find "$output_dir" -name "*.mp4" 2>/dev/null | head -1)

        if [ -f "$output_file" ]; then
            local compressed_size=$(stat -c%s "$output_file")
            local compressed_size_mb=$((compressed_size / 1024 / 1024))
            local compression_ratio=$(echo "scale=1; $compressed_size * 100 / $original_size_bytes" | bc)

            case $method in
                h265_crf12) echo "| H.265 CRF 12 | libx265 | CRF 12 | ${compressed_size_mb} MB | ${compression_ratio}% | ⭐⭐⭐⭐⭐ | 现代浏览器 |" >> "$report_file" ;;
                h265_crf14) echo "| H.265 CRF 14 | libx265 | CRF 14 | ${compressed_size_mb} MB | ${compression_ratio}% | ⭐⭐⭐⭐⭐ | 现代浏览器 |" >> "$report_file" ;;
                h265_2pass) echo "| H.265 两遍 | libx265 | 8000k | ${compressed_size_mb} MB | ${compression_ratio}% | ⭐⭐⭐⭐⭐ | 现代浏览器 |" >> "$report_file" ;;
                h265_hybrid) echo "| H.265 混合 | libx265 | CRF 14+滤镜 | ${compressed_size_mb} MB | ${compression_ratio}% | ⭐⭐⭐⭐⭐ | 现代浏览器 |" >> "$report_file" ;;
                av1_crf15) echo "| AV1 CRF 15 | libaom-av1 | CRF 15 | ${compressed_size_mb} MB | ${compression_ratio}% | ⭐⭐⭐⭐⭐ | 新浏览器 |" >> "$report_file" ;;
                h264_crf16) echo "| H.264 CRF 16 | libx264 | CRF 16 | ${compressed_size_mb} MB | ${compression_ratio}% | ⭐⭐⭐⭐ | 全平台 |" >> "$report_file" ;;
            esac
        else
            case $method in
                h265_crf12) echo "| H.265 CRF 12 | libx265 | CRF 12 | 压缩失败 | - | - | - |" >> "$report_file" ;;
                h265_crf14) echo "| H.265 CRF 14 | libx265 | CRF 14 | 压缩失败 | - | - | - |" >> "$report_file" ;;
                h265_2pass) echo "| H.265 两遍 | libx265 | 8000k | 压缩失败 | - | - | - |" >> "$report_file" ;;
                h265_hybrid) echo "| H.265 混合 | libx265 | CRF 14+滤镜 | 压缩失败 | - | - | - |" >> "$report_file" ;;
                av1_crf15) echo "| AV1 CRF 15 | libaom-av1 | CRF 15 | 压缩失败 | - | - | - |" >> "$report_file" ;;
                h264_crf16) echo "| H.264 CRF 16 | libx264 | CRF 16 | 压缩失败 | - | - | - |" >> "$report_file" ;;
            esac
        fi
    done

    # VP9 单独处理 (webm格式)
    local vp9_file="compressed_home/vp9_crf18/home_vp9_crf18.webm"
    if [ -f "$vp9_file" ]; then
        local vp9_size=$(stat -c%s "$vp9_file")
        local vp9_size_mb=$((vp9_size / 1024 / 1024))
        local vp9_ratio=$(echo "scale=1; $vp9_size * 100 / $original_size_bytes" | bc)
        echo "| VP9 CRF 18 | libvpx-vp9 | CRF 18 | ${vp9_size_mb} MB | ${vp9_ratio}% | ⭐⭐⭐⭐ | Chrome/Firefox |" >> "$report_file"
    else
        echo "| VP9 CRF 18 | libvpx-vp9 | CRF 18 | 压缩失败 | - | - | - |" >> "$report_file"
    fi

    cat >> "$report_file" << EOF

## 推荐方案分析

### 🏆 最佳画质保持
- **H.265 CRF 12**: 接近无损，适合存档
- **H.265 CRF 14**: 推荐方案，最佳质量/大小平衡

### 🚀 最优压缩效率
- **AV1 CRF 15**: 最新技术，最小文件大小
- **H.265 混合**: 高级滤镜优化

### 🌐 最佳兼容性
- **H.264 CRF 16**: 全平台支持
- **VP9 CRF 18**: 开源方案，Chrome/Firefox原生支持

## 技术参数

- **线程数**: 2 (双线程)
- **分辨率**: 2560x1440 (保持原始)
- **音频**: 已移除 (-an)
- **像素格式**: yuv420p
- **色彩空间**: bt709

## 文件结构

\`\`\`
compressed_home/
├── h265_crf12/home_h265_crf12.mp4
├── h265_crf14/home_h265_crf14.mp4
├── h265_2pass/home_h265_2pass.mp4
├── h265_hybrid/home_h265_hybrid.mp4
├── av1_crf15/home_av1_crf15.mp4
├── vp9_crf18/home_vp9_crf18.webm
├── h264_crf16/home_h264_crf16.mp4
└── HOME_COMPRESSION_REPORT.md (本文件)
\`\`\`

生成时间: $(date '+%Y-%m-%d %H:%M:%S')
基于《压缩方案选择.md》实施
EOF

    log_success "压缩报告已生成: $report_file"
}

# 主函数
main() {
    local start_time=$(date +%s)

    log_info "🎬 开始 Home 视频全方案压缩"
    log_info "原始文件: $INPUT_FILE ($ORIGINAL_SIZE, 15秒)"
    log_info "分辨率: $RESOLUTION (保持原始)"
    log_info "使用双线程压缩"

    # 检查输入文件
    if [ ! -f "$INPUT_FILE" ]; then
        log_error "输入文件不存在: $INPUT_FILE"
        exit 1
    fi

    # 检查依赖
    if ! command -v ffmpeg &> /dev/null; then
        log_error "ffmpeg 未安装"
        exit 1
    fi

    if ! command -v bc &> /dev/null; then
        log_error "bc 未安装 (用于计算压缩比)"
        exit 1
    fi

    # 创建输出目录
    create_output_dirs

    # 执行所有压缩方案
    log_info "=== 开始执行所有7种压缩方案 ==="

    compress_h265_crf12
    compress_h265_crf14
    compress_h265_2pass
    compress_h265_hybrid
    compress_av1
    compress_vp9
    compress_h264

    # 生成报告
    generate_report

    # 计算总耗时
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local minutes=$((duration / 60))
    local seconds=$((duration % 60))

    log_success "🎉 Home 视频全方案压缩完成！"
    log_success "总耗时: ${minutes}分 ${seconds}秒"
    log_success "查看报告: compressed_home/HOME_COMPRESSION_REPORT.md"

    # 显示文件大小对比
    echo ""
    log_info "📊 文件大小对比:"
    echo "原始文件: $(du -h "$INPUT_FILE")"
    find compressed_home -name "*.mp4" -o -name "*.webm" | sort | while read file; do
        echo "$(basename "$(dirname "$file")"): $(du -h "$file")"
    done
}

# 执行主函数
main "$@"
