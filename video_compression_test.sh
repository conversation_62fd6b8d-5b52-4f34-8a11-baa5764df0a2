#!/bin/bash

# 🎬 Video Compression Testing Suite
# Comprehensive testing for optimal Cloudinary upload compression
# Based on compression strategy document

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
OUTPUT_DIR="compression_tests"
RESULTS_FILE="compression_results.txt"
DETAILED_LOG="compression_detailed.log"
TARGET_RESOLUTION="2560:1440"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}========================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}========================================${NC}"
}

# Function to get file size in MB
get_file_size() {
    local file="$1"
    if [[ -f "$file" ]]; then
        local size_bytes=$(stat -c%s "$file")
        local size_mb=$(echo "scale=2; $size_bytes / 1024 / 1024" | bc)
        echo "$size_mb"
    else
        echo "0"
    fi
}

# Function to get video info
get_video_info() {
    local input_file="$1"
    print_status "Analyzing input video: $input_file"
    
    # Get video information
    local duration=$(ffprobe -v quiet -show_entries format=duration -of csv=p=0 "$input_file")
    local resolution=$(ffprobe -v quiet -select_streams v:0 -show_entries stream=width,height -of csv=s=x:p=0 "$input_file")
    local bitrate=$(ffprobe -v quiet -show_entries format=bit_rate -of csv=p=0 "$input_file")
    local codec=$(ffprobe -v quiet -select_streams v:0 -show_entries stream=codec_name -of csv=p=0 "$input_file")
    
    echo "Original Info:"
    echo "  Duration: ${duration}s"
    echo "  Resolution: ${resolution}"
    echo "  Bitrate: ${bitrate} bps"
    echo "  Codec: ${codec}"
    echo "  Size: $(get_file_size "$input_file") MB"
    echo ""
}

# Function to run compression test
run_compression_test() {
    local input_file="$1"
    local output_file="$2"
    local method_name="$3"
    local ffmpeg_cmd="$4"
    
    print_header "Testing: $method_name"
    print_status "Input: $input_file"
    print_status "Output: $output_file"
    
    local start_time=$(date +%s)
    
    # Run FFmpeg command
    echo "Command: $ffmpeg_cmd" >> "$DETAILED_LOG"
    if eval "$ffmpeg_cmd" >> "$DETAILED_LOG" 2>&1; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        local output_size=$(get_file_size "$output_file")
        local input_size=$(get_file_size "$input_file")
        local compression_ratio=$(echo "scale=2; $input_size / $output_size" | bc)
        local size_reduction=$(echo "scale=1; (1 - $output_size / $input_size) * 100" | bc)
        
        print_success "Compression completed in ${duration}s"
        print_success "Output size: ${output_size} MB"
        print_success "Compression ratio: ${compression_ratio}:1"
        print_success "Size reduction: ${size_reduction}%"
        
        # Log results
        echo "$method_name|$output_size|$compression_ratio|$size_reduction|$duration" >> "$RESULTS_FILE"
        
        echo ""
    else
        print_error "Compression failed for $method_name"
        echo "$method_name|FAILED|0|0|0" >> "$RESULTS_FILE"
        echo ""
    fi
}

# Main compression testing function
test_all_compressions() {
    local input_file="$1"
    local base_name=$(basename "$input_file" .mp4)
    
    # Initialize results file
    echo "Method|Size(MB)|Ratio|Reduction(%)|Time(s)" > "$RESULTS_FILE"
    echo "=== Compression Test Started: $(date) ===" > "$DETAILED_LOG"
    
    print_header "🎬 COMPREHENSIVE VIDEO COMPRESSION TEST"
    get_video_info "$input_file"
    
    # Test 1: H.265 CRF 12 (Near Lossless)
    run_compression_test "$input_file" \
        "$OUTPUT_DIR/${base_name}_h265_crf12.mp4" \
        "H.265 CRF 12 (Near Lossless)" \
        "ffmpeg -y -i '$input_file' -c:v libx265 -crf 12 -preset slower -profile:v main -level 5.1 -x265-params 'me=umh:subme=3:ref=5:bframes=8:rd=3:psy-rd=1.0:aq-mode=3' -vf 'scale=$TARGET_RESOLUTION:flags=lanczos:param0=3' -colorspace bt709 -color_primaries bt709 -color_trc bt709 -an -movflags +faststart -pix_fmt yuv420p '$OUTPUT_DIR/${base_name}_h265_crf12.mp4'"
    
    # Test 2: H.265 CRF 14 (High Quality)
    run_compression_test "$input_file" \
        "$OUTPUT_DIR/${base_name}_h265_crf14.mp4" \
        "H.265 CRF 14 (High Quality)" \
        "ffmpeg -y -i '$input_file' -c:v libx265 -crf 14 -preset slower -profile:v main -level 5.1 -x265-params 'me=umh:subme=3:ref=6:bframes=8:b-adapt=2:rd=4:psy-rd=1.0:psy-rdoq=2.0:aq-mode=3:aq-strength=0.8:qcomp=0.6:deblock=1,1:sao=1:limit-sao=1' -vf 'hqdn3d=1:0.5:1:0.5,scale=$TARGET_RESOLUTION:flags=lanczos:param0=3' -an -movflags +faststart -pix_fmt yuv420p '$OUTPUT_DIR/${base_name}_h265_crf14.mp4'"
    
    # Test 3: H.265 Two-Pass (Precise Control)
    print_header "H.265 Two-Pass (Precise Control)"
    print_status "Running first pass..."
    if ffmpeg -y -i "$input_file" -c:v libx265 -b:v 9000k -preset slower -pass 1 -f null /dev/null >> "$DETAILED_LOG" 2>&1; then
        print_status "Running second pass..."
        run_compression_test "$input_file" \
            "$OUTPUT_DIR/${base_name}_h265_2pass.mp4" \
            "H.265 Two-Pass 9000k" \
            "ffmpeg -y -i '$input_file' -c:v libx265 -b:v 9000k -preset slower -pass 2 -profile:v main -level 5.1 -vf 'scale=$TARGET_RESOLUTION:flags=lanczos' -an -movflags +faststart -pix_fmt yuv420p '$OUTPUT_DIR/${base_name}_h265_2pass.mp4'"
        # Clean up pass files
        rm -f ffmpeg2pass-*.log
    else
        print_error "Two-pass encoding failed"
    fi

    # Test 4: AV1 (Latest Technology)
    if command -v ffmpeg >/dev/null 2>&1 && ffmpeg -encoders 2>/dev/null | grep -q libaom-av1; then
        run_compression_test "$input_file" \
            "$OUTPUT_DIR/${base_name}_av1_crf15.mp4" \
            "AV1 CRF 15 (Latest Tech)" \
            "ffmpeg -y -i '$input_file' -c:v libaom-av1 -crf 15 -cpu-used 1 -row-mt 1 -tiles 4x2 -g 240 -keyint_min 23 -aq-mode 1 -tune-content 0 -vf 'scale=$TARGET_RESOLUTION:flags=lanczos' -an -pix_fmt yuv420p '$OUTPUT_DIR/${base_name}_av1_crf15.mp4'"
    else
        print_warning "AV1 encoder not available, skipping AV1 test"
    fi

    # Test 5: VP9 (Google Open Source)
    if command -v ffmpeg >/dev/null 2>&1 && ffmpeg -encoders 2>/dev/null | grep -q libvpx-vp9; then
        run_compression_test "$input_file" \
            "$OUTPUT_DIR/${base_name}_vp9_crf18.mp4" \
            "VP9 CRF 18 (Google)" \
            "ffmpeg -y -i '$input_file' -c:v libvpx-vp9 -crf 18 -b:v 0 -cpu-used 2 -row-mt 1 -tile-columns 2 -frame-parallel 1 -an -pix_fmt yuv420p '$OUTPUT_DIR/${base_name}_vp9_crf18.mp4'"
    else
        print_warning "VP9 encoder not available, skipping VP9 test"
    fi

    # Test 6: H.264 CRF 16 (Traditional High Quality)
    run_compression_test "$input_file" \
        "$OUTPUT_DIR/${base_name}_h264_crf16.mp4" \
        "H.264 CRF 16 (Traditional)" \
        "ffmpeg -y -i '$input_file' -c:v libx264 -crf 16 -preset veryslow -profile:v high -level 5.1 -vf 'scale=$TARGET_RESOLUTION:flags=lanczos' -an -movflags +faststart -pix_fmt yuv420p '$OUTPUT_DIR/${base_name}_h264_crf16.mp4'"

    # Test 7: H.265 CRF 16 (Balanced)
    run_compression_test "$input_file" \
        "$OUTPUT_DIR/${base_name}_h265_crf16.mp4" \
        "H.265 CRF 16 (Balanced)" \
        "ffmpeg -y -i '$input_file' -c:v libx265 -crf 16 -preset slow -profile:v main -level 5.1 -vf 'scale=$TARGET_RESOLUTION:flags=lanczos' -an -movflags +faststart -pix_fmt yuv420p '$OUTPUT_DIR/${base_name}_h265_crf16.mp4'"

    # Generate comprehensive report
    generate_report "$input_file" "$base_name"
}

# Function to generate detailed analysis report
generate_report() {
    local input_file="$1"
    local base_name="$2"
    local report_file="$OUTPUT_DIR/${base_name}_compression_report.md"
    local input_size=$(get_file_size "$input_file")

    print_header "📊 GENERATING COMPREHENSIVE REPORT"

    cat > "$report_file" << EOF
# 🎬 Video Compression Test Report

**Input File**: \`$input_file\`
**Original Size**: ${input_size} MB
**Target Resolution**: $TARGET_RESOLUTION
**Test Date**: $(date)

## 📊 Compression Results Summary

| Method | Size (MB) | Compression Ratio | Size Reduction | Encoding Time | Quality Score |
|--------|-----------|-------------------|----------------|---------------|---------------|
EOF

    # Process results and add to report
    if [[ -f "$RESULTS_FILE" ]]; then
        tail -n +2 "$RESULTS_FILE" | while IFS='|' read -r method size ratio reduction time; do
            if [[ "$method" != "FAILED" && "$size" != "FAILED" ]]; then
                # Calculate quality score (higher is better)
                local quality_score=$(echo "scale=1; ($ratio * 2) + (100 - $reduction) / 10" | bc 2>/dev/null || echo "N/A")
                echo "| $method | $size | ${ratio}:1 | ${reduction}% | ${time}s | $quality_score |" >> "$report_file"
            else
                echo "| $method | FAILED | - | - | - | - |" >> "$report_file"
            fi
        done
    fi

    cat >> "$report_file" << EOF

## 🏆 Recommendations

### For Cloudinary Upload (Best Balance):
EOF

    # Find best options
    local best_h265=$(find_best_option "H.265")
    local best_overall=$(find_best_overall)

    cat >> "$report_file" << EOF
- **Recommended**: $best_h265
- **Alternative**: $best_overall

### Quality vs Size Analysis:
- **Maximum Quality**: H.265 CRF 12 (Near lossless, larger file)
- **Best Balance**: H.265 CRF 14-16 (Excellent quality, reasonable size)
- **Smallest Size**: AV1 CRF 15 (Best compression, newer codec)

### Browser Compatibility:
- **Universal**: H.264 (All browsers and devices)
- **Modern**: H.265 (Most modern browsers)
- **Cutting Edge**: AV1 (Latest browsers only)

## 📈 Detailed Analysis

### File Size Comparison:
EOF

    # Add file size comparison
    if [[ -f "$RESULTS_FILE" ]]; then
        echo "- Original: ${input_size} MB (100%)" >> "$report_file"
        tail -n +2 "$RESULTS_FILE" | while IFS='|' read -r method size ratio reduction time; do
            if [[ "$size" != "FAILED" ]]; then
                local percentage=$(echo "scale=1; $size / $input_size * 100" | bc)
                echo "- $method: ${size} MB (${percentage}%)" >> "$report_file"
            fi
        done
    fi

    cat >> "$report_file" << EOF

### Encoding Performance:
EOF

    # Add encoding time analysis
    if [[ -f "$RESULTS_FILE" ]]; then
        tail -n +2 "$RESULTS_FILE" | while IFS='|' read -r method size ratio reduction time; do
            if [[ "$time" != "0" && "$time" != "FAILED" ]]; then
                local minutes=$(echo "scale=1; $time / 60" | bc)
                echo "- $method: ${time}s (${minutes} min)" >> "$report_file"
            fi
        done
    fi

    cat >> "$report_file" << EOF

## 🎯 Final Recommendation for Cloudinary

Based on the test results, the optimal choice for Cloudinary upload considering:
- Maximum visual quality retention
- Reasonable file size for web delivery
- Good browser compatibility
- Acceptable encoding time

**Recommended Method**: Check the results above and choose H.265 CRF 14-16 for best balance.

---
*Report generated by Video Compression Test Suite*
EOF

    print_success "Report generated: $report_file"

    # Display summary
    print_header "📊 QUICK SUMMARY"
    echo -e "${CYAN}Original Size:${NC} ${input_size} MB"
    echo ""
    echo -e "${CYAN}Top 3 Results:${NC}"

    # Show top 3 results sorted by quality score
    if [[ -f "$RESULTS_FILE" ]]; then
        {
            echo "Quality|Method|Size|Ratio|Reduction"
            tail -n +2 "$RESULTS_FILE" | while IFS='|' read -r method size ratio reduction time; do
                if [[ "$size" != "FAILED" ]]; then
                    local quality_score=$(echo "scale=1; ($ratio * 2) + (100 - $reduction) / 10" | bc 2>/dev/null || echo "0")
                    echo "$quality_score|$method|$size|$ratio|$reduction"
                fi
            done
        } | sort -t'|' -k1 -nr | head -3 | while IFS='|' read -r quality method size ratio reduction; do
            echo -e "  ${GREEN}✓${NC} $method: ${size}MB (${ratio}:1 compression, ${reduction}% smaller)"
        done
    fi

    echo ""
    print_success "All tests completed! Check $report_file for detailed analysis."
}

# Helper function to find best H.265 option
find_best_option() {
    local codec="$1"
    if [[ -f "$RESULTS_FILE" ]]; then
        grep "$codec" "$RESULTS_FILE" | head -1 | cut -d'|' -f1 || echo "H.265 CRF 14"
    else
        echo "H.265 CRF 14"
    fi
}

# Helper function to find best overall option
find_best_overall() {
    if [[ -f "$RESULTS_FILE" ]]; then
        tail -n +2 "$RESULTS_FILE" | while IFS='|' read -r method size ratio reduction time; do
            if [[ "$size" != "FAILED" ]]; then
                local quality_score=$(echo "scale=1; ($ratio * 2) + (100 - $reduction) / 10" | bc 2>/dev/null || echo "0")
                echo "$quality_score|$method"
            fi
        done | sort -t'|' -k1 -nr | head -1 | cut -d'|' -f2 || echo "H.265 CRF 14"
    else
        echo "H.265 CRF 14"
    fi
}

# Function to check dependencies
check_dependencies() {
    print_status "Checking dependencies..."

    local missing_deps=()

    # Check for required tools
    if ! command -v ffmpeg >/dev/null 2>&1; then
        missing_deps+=("ffmpeg")
    fi

    if ! command -v ffprobe >/dev/null 2>&1; then
        missing_deps+=("ffprobe")
    fi

    if ! command -v bc >/dev/null 2>&1; then
        missing_deps+=("bc")
    fi

    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_error "Missing dependencies: ${missing_deps[*]}"
        echo "Please install missing dependencies:"
        echo "  Ubuntu/Debian: sudo apt-get install ffmpeg bc"
        echo "  CentOS/RHEL: sudo yum install ffmpeg bc"
        echo "  macOS: brew install ffmpeg bc"
        exit 1
    fi

    # Check FFmpeg encoders
    print_status "Checking available encoders..."
    local encoders=$(ffmpeg -encoders 2>/dev/null)

    if echo "$encoders" | grep -q libx265; then
        print_success "H.265 encoder available"
    else
        print_warning "H.265 encoder not available"
    fi

    if echo "$encoders" | grep -q libaom-av1; then
        print_success "AV1 encoder available"
    else
        print_warning "AV1 encoder not available"
    fi

    if echo "$encoders" | grep -q libvpx-vp9; then
        print_success "VP9 encoder available"
    else
        print_warning "VP9 encoder not available"
    fi

    echo ""
}

# Function to show usage
show_usage() {
    echo "🎬 Video Compression Testing Suite"
    echo ""
    echo "Usage: $0 [OPTIONS] <input_video>"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -o, --output DIR        Output directory (default: compression_tests)"
    echo "  -r, --resolution WxH    Target resolution (default: 2560:1440)"
    echo "  --quick                 Run only essential tests (H.265 CRF 14, 16)"
    echo "  --all                   Run all available tests (default)"
    echo ""
    echo "Examples:"
    echo "  $0 anniversary/anniversary.mp4"
    echo "  $0 -o my_tests -r 1920:1080 video.mp4"
    echo "  $0 --quick video.mp4"
    echo ""
    echo "Supported formats: MP4, MOV, AVI, MKV, etc."
    echo ""
}

# Quick test mode (only essential tests)
test_quick_compressions() {
    local input_file="$1"
    local base_name=$(basename "$input_file" .mp4)

    # Initialize results file
    echo "Method|Size(MB)|Ratio|Reduction(%)|Time(s)" > "$RESULTS_FILE"
    echo "=== Quick Compression Test Started: $(date) ===" > "$DETAILED_LOG"

    print_header "🎬 QUICK VIDEO COMPRESSION TEST"
    get_video_info "$input_file"

    # Test 1: H.265 CRF 14 (Recommended)
    run_compression_test "$input_file" \
        "$OUTPUT_DIR/${base_name}_h265_crf14.mp4" \
        "H.265 CRF 14 (Recommended)" \
        "ffmpeg -y -i '$input_file' -c:v libx265 -crf 14 -preset slow -profile:v main -level 5.1 -vf 'scale=$TARGET_RESOLUTION:flags=lanczos' -an -movflags +faststart -pix_fmt yuv420p '$OUTPUT_DIR/${base_name}_h265_crf14.mp4'"

    # Test 2: H.265 CRF 16 (Balanced)
    run_compression_test "$input_file" \
        "$OUTPUT_DIR/${base_name}_h265_crf16.mp4" \
        "H.265 CRF 16 (Balanced)" \
        "ffmpeg -y -i '$input_file' -c:v libx265 -crf 16 -preset slow -profile:v main -level 5.1 -vf 'scale=$TARGET_RESOLUTION:flags=lanczos' -an -movflags +faststart -pix_fmt yuv420p '$OUTPUT_DIR/${base_name}_h265_crf16.mp4'"

    # Test 3: H.264 CRF 18 (Compatible)
    run_compression_test "$input_file" \
        "$OUTPUT_DIR/${base_name}_h264_crf18.mp4" \
        "H.264 CRF 18 (Compatible)" \
        "ffmpeg -y -i '$input_file' -c:v libx264 -crf 18 -preset slow -profile:v high -level 5.1 -vf 'scale=$TARGET_RESOLUTION:flags=lanczos' -an -movflags +faststart -pix_fmt yuv420p '$OUTPUT_DIR/${base_name}_h264_crf18.mp4'"

    # Generate report
    generate_report "$input_file" "$base_name"
}

# Main function
main() {
    local input_file=""
    local quick_mode=false

    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -o|--output)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            -r|--resolution)
                TARGET_RESOLUTION="$2"
                shift 2
                ;;
            --quick)
                quick_mode=true
                shift
                ;;
            --all)
                quick_mode=false
                shift
                ;;
            -*)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
            *)
                if [[ -z "$input_file" ]]; then
                    input_file="$1"
                else
                    print_error "Multiple input files not supported"
                    exit 1
                fi
                shift
                ;;
        esac
    done

    # Validate input
    if [[ -z "$input_file" ]]; then
        print_error "No input file specified"
        show_usage
        exit 1
    fi

    if [[ ! -f "$input_file" ]]; then
        print_error "Input file not found: $input_file"
        exit 1
    fi

    # Check dependencies
    check_dependencies

    # Create output directory
    mkdir -p "$OUTPUT_DIR"

    # Run tests
    if [[ "$quick_mode" == true ]]; then
        test_quick_compressions "$input_file"
    else
        test_all_compressions "$input_file"
    fi
}

# Run main function with all arguments
main "$@"
}
