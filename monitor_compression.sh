#!/bin/bash

# 🎬 压缩进度监控脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 清屏函数
clear_screen() {
    clear
    echo -e "${CYAN}🎬 全方案视频压缩监控 - $(date '+%Y-%m-%d %H:%M:%S')${NC}"
    echo "=================================================================="
}

# 显示压缩进度
show_progress() {
    clear_screen
    
    echo -e "${BLUE}📊 当前压缩状态:${NC}"
    echo ""
    
    # 检查是否有压缩进程在运行
    if pgrep -f "comprehensive_compression.sh" > /dev/null; then
        echo -e "${GREEN}✅ 压缩进程正在运行${NC}"
        
        # 显示当前正在处理的文件
        if [ -d "logs" ]; then
            echo -e "${YELLOW}📝 最新日志文件:${NC}"
            ls -lt logs/*.log 2>/dev/null | head -3
            echo ""
            
            # 显示最新日志的最后几行
            latest_log=$(ls -t logs/*.log 2>/dev/null | head -1)
            if [ -n "$latest_log" ]; then
                echo -e "${BLUE}📄 当前进度 ($latest_log):${NC}"
                tail -5 "$latest_log" | grep -E "(frame=|fps=)" | tail -1
                echo ""
            fi
        fi
    else
        echo -e "${RED}❌ 压缩进程未运行${NC}"
    fi
    
    # 显示已完成的文件
    if [ -d "compressed" ]; then
        echo -e "${PURPLE}📁 压缩结果统计:${NC}"
        echo ""
        
        for video_dir in compressed/*/; do
            if [ -d "$video_dir" ]; then
                video_name=$(basename "$video_dir")
                echo -e "${CYAN}📹 $video_name:${NC}"
                
                # 统计各方案完成情况
                local completed=0
                local total=7
                
                for method in h265_crf12 h265_crf14 h265_2pass h265_hybrid av1_crf15 vp9_crf18 h264_crf16; do
                    method_dir="${video_dir}${method}"
                    if [ -d "$method_dir" ]; then
                        # 检查是否有非空的输出文件
                        output_file=$(find "$method_dir" -name "*.mp4" -o -name "*.webm" -size +0c 2>/dev/null | head -1)
                        if [ -n "$output_file" ]; then
                            file_size=$(du -h "$output_file" 2>/dev/null | cut -f1)
                            echo -e "  ✅ $method: ${file_size}"
                            ((completed++))
                        else
                            echo -e "  ⏳ $method: 进行中..."
                        fi
                    else
                        echo -e "  ⏸️  $method: 未开始"
                    fi
                done
                
                # 显示完成百分比
                local percentage=$((completed * 100 / total))
                echo -e "  📊 完成度: ${completed}/${total} (${percentage}%)"
                echo ""
            fi
        done
    else
        echo -e "${YELLOW}📂 压缩输出目录尚未创建${NC}"
    fi
    
    # 显示系统资源使用情况
    echo -e "${BLUE}💻 系统资源使用:${NC}"
    echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
    echo "内存使用: $(free -h | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
    echo "磁盘使用: $(df -h . | awk 'NR==2{print $5}')"
    echo ""
    
    # 显示预估完成时间
    if pgrep -f "comprehensive_compression.sh" > /dev/null; then
        echo -e "${YELLOW}⏰ 预估信息:${NC}"
        echo "• 总共需要处理: 5个视频 × 7种方案 = 35个压缩任务"
        echo "• 每个任务预估时间: 10-30分钟 (取决于视频长度和方案复杂度)"
        echo "• 预估总时间: 6-15小时 (使用双线程并行)"
        echo ""
    fi
}

# 主循环
main() {
    echo -e "${GREEN}🎬 开始监控压缩进度...${NC}"
    echo "按 Ctrl+C 退出监控"
    echo ""
    
    while true; do
        show_progress
        
        # 检查是否完成
        if ! pgrep -f "comprehensive_compression.sh" > /dev/null; then
            if [ -f "compressed/COMPRESSION_SUMMARY.md" ]; then
                echo -e "${GREEN}🎉 所有压缩任务已完成！${NC}"
                echo -e "${BLUE}📋 查看总结报告: compressed/COMPRESSION_SUMMARY.md${NC}"
                break
            fi
        fi
        
        # 等待30秒后刷新
        sleep 30
    done
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
