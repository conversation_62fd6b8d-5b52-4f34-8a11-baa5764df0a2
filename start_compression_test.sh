#!/bin/bash

# 🎬 Interactive Video Compression Test Launcher
# Easy-to-use interface for video compression testing

set -e

# Color codes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_header() { echo -e "${PURPLE}========== $1 ==========${NC}"; }
print_menu() { echo -e "${CYAN}$1${NC}"; }

# Show banner
show_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                🎬 Video Compression Test Suite               ║"
    echo "║                                                              ║"
    echo "║           Find the best compression for Cloudinary!         ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}

# Check dependencies
check_dependencies() {
    local missing=()
    
    if ! command -v ffmpeg >/dev/null 2>&1; then
        missing+=("ffmpeg")
    fi
    
    if ! command -v bc >/dev/null 2>&1; then
        missing+=("bc")
    fi
    
    if [[ ${#missing[@]} -gt 0 ]]; then
        print_error "Missing dependencies: ${missing[*]}"
        echo ""
        echo "Please install missing dependencies:"
        echo "  Ubuntu/Debian: sudo apt-get install ${missing[*]}"
        echo "  CentOS/RHEL: sudo yum install ${missing[*]}"
        echo "  macOS: brew install ${missing[*]}"
        echo ""
        exit 1
    fi
}

# Find available videos
find_videos() {
    find . -maxdepth 2 -name "*.mp4" -o -name "*.MP4" -o -name "*.mov" -o -name "*.MOV" | grep -v compression | sort
}

# Show video selection menu
show_video_menu() {
    local videos=($1)
    
    if [[ ${#videos[@]} -eq 0 ]]; then
        print_error "No video files found!"
        echo "Please ensure you have .mp4 or .mov files in the current directory or subdirectories."
        exit 1
    fi
    
    print_header "Available Videos"
    echo ""
    
    local i=1
    for video in "${videos[@]}"; do
        local size=$(stat -c%s "$video" 2>/dev/null | awk '{print int($1/1024/1024)}' || echo "?")
        local dir=$(dirname "$video" | sed 's|^\./||')
        local name=$(basename "$video")
        
        if [[ "$dir" == "." ]]; then
            print_menu "$i) $name (${size}MB)"
        else
            print_menu "$i) $dir/$name (${size}MB)"
        fi
        ((i++))
    done
    
    echo ""
    print_menu "A) Test ALL videos (batch mode)"
    echo ""
}

# Show test method menu
show_method_menu() {
    print_header "Compression Test Methods"
    echo ""
    print_menu "1) 🚀 Quick Test (3 methods, ~5 minutes)"
    print_menu "   - H.265 CRF 14 (Recommended)"
    print_menu "   - H.265 CRF 16 (Smaller)"
    print_menu "   - H.264 CRF 18 (Compatible)"
    echo ""
    print_menu "2) 📊 Comprehensive Test (7+ methods, ~30 minutes)"
    print_menu "   - All H.265 variants"
    print_menu "   - AV1 (if available)"
    print_menu "   - VP9 (if available)"
    print_menu "   - Two-pass encoding"
    echo ""
    print_menu "3) ⚡ Essential Only (2 methods, ~3 minutes)"
    print_menu "   - H.265 CRF 14"
    print_menu "   - H.265 CRF 16"
    echo ""
}

# Get user choice
get_choice() {
    local prompt="$1"
    local max_choice="$2"
    local choice

    while true; do
        printf "%s" "$prompt"
        read -r choice

        if [[ "$choice" =~ ^[0-9]+$ ]] && [[ $choice -ge 1 ]] && [[ $choice -le $max_choice ]]; then
            echo "$choice"
            return
        elif [[ "$choice" == "A" || "$choice" == "a" ]]; then
            echo "A"
            return
        else
            print_error "Invalid choice. Please enter a number between 1 and $max_choice, or 'A' for all."
            echo ""
        fi
    done
}

# Run selected test
run_test() {
    local video="$1"
    local method="$2"
    
    print_header "Starting Compression Test"
    print_info "Video: $video"
    print_info "Method: $method"
    echo ""
    
    case $method in
        1)
            print_info "Running Quick Test..."
            ./quick_compress_test.sh "$video"
            ;;
        2)
            print_info "Running Comprehensive Test..."
            ./video_compression_test.sh "$video"
            ;;
        3)
            print_info "Running Essential Test..."
            ./video_compression_test.sh --quick "$video"
            ;;
    esac
}

# Run batch test
run_batch_test() {
    print_header "Starting Batch Test"
    print_info "Testing all videos with H.265 CRF 14 (recommended for Cloudinary)"
    echo ""
    
    ./batch_compress_test.sh
}

# Main interactive loop
main() {
    show_banner
    
    # Check dependencies
    check_dependencies
    
    # Find videos
    local videos=($(find_videos))
    
    while true; do
        # Show video menu
        show_video_menu "${videos[*]}"
        
        # Get video choice
        local video_choice=$(get_choice "Select video (1-${#videos[@]}, A for all): " ${#videos[@]})
        
        if [[ "$video_choice" == "A" ]]; then
            # Batch mode
            run_batch_test
            break
        else
            # Single video mode
            local selected_video="${videos[$((video_choice-1))]}"
            
            echo ""
            show_method_menu
            
            # Get method choice
            local method_choice=$(get_choice "Select test method (1-3): " 3)
            
            echo ""
            run_test "$selected_video" "$method_choice"
            break
        fi
    done
    
    echo ""
    print_success "Test completed! Check the output files for results."
    print_info "💡 Tip: Look for the .md report files for detailed analysis and recommendations."
}

# Handle Ctrl+C gracefully
trap 'echo -e "\n${YELLOW}Test interrupted by user${NC}"; exit 1' INT

# Run main function
main "$@"
