#!/bin/bash

# 📋 Video Compression Tools Overview
# Display all available tools and their usage

# Color codes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() { echo -e "${PURPLE}$1${NC}"; }
print_tool() { echo -e "${CYAN}$1${NC}"; }
print_desc() { echo -e "${BLUE}  $1${NC}"; }
print_usage() { echo -e "${GREEN}  Usage: $1${NC}"; }
print_note() { echo -e "${YELLOW}  Note: $1${NC}"; }

# Show banner
echo -e "${PURPLE}"
echo "╔══════════════════════════════════════════════════════════════════════════════╗"
echo "║                    🎬 Video Compression Testing Suite                       ║"
echo "║                                                                              ║"
echo "║              Complete toolkit for finding optimal Cloudinary settings       ║"
echo "╚══════════════════════════════════════════════════════════════════════════════╝"
echo -e "${NC}"
echo ""

# Check if tools exist
check_tool() {
    if [[ -f "$1" && -x "$1" ]]; then
        echo "✅"
    else
        echo "❌"
    fi
}

print_header "📋 AVAILABLE TOOLS"
echo ""

print_tool "🎯 start_compression_test.sh $(check_tool "start_compression_test.sh")"
print_desc "Interactive launcher with guided menu system"
print_desc "Perfect for beginners - automatically detects videos and guides through options"
print_usage "./start_compression_test.sh"
print_note "Recommended starting point for new users"
echo ""

print_tool "🔧 install_dependencies.sh $(check_tool "install_dependencies.sh")"
print_desc "Automatic dependency installer for FFmpeg and required tools"
print_desc "Detects your OS and installs appropriate packages"
print_usage "./install_dependencies.sh"
print_note "Run this first if you don't have FFmpeg installed"
echo ""

print_tool "🚀 quick_compress_test.sh $(check_tool "quick_compress_test.sh")"
print_desc "Fast testing with 3 essential compression methods"
print_desc "Results in ~5 minutes, perfect for single video testing"
print_usage "./quick_compress_test.sh <video_file>"
print_usage "Example: ./quick_compress_test.sh anniversary/anniversary.mp4"
print_note "Best for quick evaluation of a single video"
echo ""

print_tool "📊 video_compression_test.sh $(check_tool "video_compression_test.sh")"
print_desc "Comprehensive testing suite with all compression methods"
print_desc "Tests H.265, AV1, VP9, H.264 with detailed analysis"
print_usage "./video_compression_test.sh [options] <video_file>"
print_usage "Options:"
print_usage "  --quick                 Essential tests only"
print_usage "  -o DIR                  Custom output directory"
print_usage "  -r WIDTHxHEIGHT        Custom resolution"
print_usage "Example: ./video_compression_test.sh --quick video.mp4"
print_note "Generates detailed .md reports with recommendations"
echo ""

print_tool "📦 batch_compress_test.sh $(check_tool "batch_compress_test.sh")"
print_desc "Batch processing for all videos in workspace"
print_desc "Automatically finds and tests all .mp4/.mov files"
print_usage "./batch_compress_test.sh"
print_note "Creates comprehensive batch summary report"
echo ""

print_tool "📋 show_tools.sh $(check_tool "show_tools.sh")"
print_desc "This help screen - shows all available tools"
print_usage "./show_tools.sh"
echo ""

print_header "📁 CURRENT WORKSPACE"
echo ""

# Show available videos
videos=($(find . -maxdepth 2 -name "*.mp4" -o -name "*.MP4" -o -name "*.mov" -o -name "*.MOV" | grep -v compression | sort))

if [[ ${#videos[@]} -gt 0 ]]; then
    print_tool "📹 Available Videos (${#videos[@]} found):"
    for video in "${videos[@]}"; do
        local size=$(stat -c%s "$video" 2>/dev/null | awk '{print int($1/1024/1024)}' || echo "?")
        local dir=$(dirname "$video" | sed 's|^\./||')
        local name=$(basename "$video")
        
        if [[ "$dir" == "." ]]; then
            print_desc "$name (${size}MB)"
        else
            print_desc "$dir/$name (${size}MB)"
        fi
    done
else
    print_tool "📹 No video files found in current directory"
    print_note "Place .mp4 or .mov files in subdirectories to test them"
fi

echo ""

print_header "🎯 RECOMMENDED WORKFLOW"
echo ""

print_desc "1. First time setup:"
print_usage "   ./install_dependencies.sh"
echo ""

print_desc "2. Interactive testing (recommended):"
print_usage "   ./start_compression_test.sh"
echo ""

print_desc "3. Quick single video test:"
print_usage "   ./quick_compress_test.sh your_video.mp4"
echo ""

print_desc "4. Comprehensive analysis:"
print_usage "   ./video_compression_test.sh your_video.mp4"
echo ""

print_desc "5. Test all videos at once:"
print_usage "   ./batch_compress_test.sh"
echo ""

print_header "📊 EXPECTED RESULTS"
echo ""

print_desc "For Cloudinary optimization, you'll typically get:"
print_desc "• H.265 CRF 14: 50-65MB, excellent quality (recommended)"
print_desc "• H.265 CRF 16: 40-55MB, very good quality (smaller files)"
print_desc "• H.264 CRF 18: 80-120MB, good quality (maximum compatibility)"
echo ""

print_desc "Output files include:"
print_desc "• Compressed video files (.mp4)"
print_desc "• Detailed analysis reports (.md)"
print_desc "• Performance logs and statistics"
echo ""

print_header "🔍 TROUBLESHOOTING"
echo ""

# Check dependencies
print_desc "Dependency Status:"
if command -v ffmpeg >/dev/null 2>&1; then
    print_desc "✅ FFmpeg installed"
else
    print_desc "❌ FFmpeg missing - run ./install_dependencies.sh"
fi

if command -v bc >/dev/null 2>&1; then
    print_desc "✅ bc calculator installed"
else
    print_desc "❌ bc missing - run ./install_dependencies.sh"
fi

echo ""
print_desc "Common issues:"
print_desc "• Permission denied: chmod +x *.sh"
print_desc "• FFmpeg not found: ./install_dependencies.sh"
print_desc "• No videos found: place .mp4/.mov files in subdirectories"
print_desc "• Out of space: compression tests create multiple large files"
echo ""

print_header "📚 DOCUMENTATION"
echo ""

print_desc "📖 README.md - Complete usage guide"
print_desc "📋 压缩方案选择.md - Detailed compression strategy (Chinese)"
print_desc "🔍 Generated .md reports - Test results and recommendations"
echo ""

print_tool "🎉 Ready to start? Run: ./start_compression_test.sh"
echo ""
