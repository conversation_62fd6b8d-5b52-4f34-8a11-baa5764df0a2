#!/bin/bash

# 🔍 Compression Progress Monitor
# Monitor all running compression tests and report progress

# Color codes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() { echo -e "${PURPLE}========== $1 ==========${NC}"; }
print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

# Get file size in MB
get_size_mb() {
    local file="$1"
    if [[ -f "$file" ]]; then
        local size_bytes=$(stat -c%s "$file")
        echo "scale=2; $size_bytes / 1024 / 1024" | bc
    else
        echo "0"
    fi
}

# Monitor function
monitor_progress() {
    local cycle=1
    
    while true; do
        clear
        print_header "🎬 COMPRESSION PROGRESS MONITOR - Cycle $cycle"
        echo "$(date)"
        echo ""
        
        # Check running processes
        local running_count=0
        local completed_count=0
        
        # Original file sizes for reference
        echo -e "${CYAN}📊 Original File Sizes:${NC}"
        echo "  meetings.mp4:     $(get_size_mb 'meetings/meetings.mp4') MB"
        echo "  home.mp4:         $(get_size_mb 'home/home.mp4') MB"
        echo "  memorial.mp4:     $(get_size_mb 'memorial/memorial.mp4') MB"
        echo "  together-days.mp4: $(get_size_mb 'together-days/together-days.mp4') MB"
        echo "  anniversary.mp4:  $(get_size_mb 'anniversary/anniversary.mp4') MB"
        echo ""
        
        # Check compression output
        if [[ -d "compression_tests" ]]; then
            echo -e "${CYAN}📁 Current Compression Results:${NC}"
            local file_count=$(find compression_tests/ -name "*.mp4" | wc -l)
            echo "  Total compressed files: $file_count"
            echo ""
            
            # Show current files sorted by size
            if [[ $file_count -gt 0 ]]; then
                echo -e "${CYAN}📋 Compressed Files (sorted by size):${NC}"
                ls -lh compression_tests/*.mp4 2>/dev/null | awk '{
                    size = $5
                    name = $9
                    gsub(/.*\//, "", name)
                    printf "  %-35s %8s\n", name, size
                }' | sort -k2 -h
                echo ""
            fi
        else
            echo -e "${YELLOW}⏳ Compression directory not created yet...${NC}"
            echo ""
        fi
        
        # Check process status
        echo -e "${CYAN}🔄 Process Status:${NC}"
        
        # Check each video's process
        local videos=("meetings" "home" "memorial" "together-days" "anniversary")
        local terminals=(2 3 4 5 6)
        
        for i in "${!videos[@]}"; do
            local video="${videos[$i]}"
            local terminal="${terminals[$i]}"
            
            # Check if process is still running (simplified check)
            if ps aux | grep -q "[v]ideo_compression_test.sh.*${video}"; then
                echo -e "  ${GREEN}✓${NC} ${video}.mp4 - Processing..."
                ((running_count++))
            else
                echo -e "  ${YELLOW}?${NC} ${video}.mp4 - Status unknown"
            fi
        done
        
        echo ""
        echo -e "${CYAN}📈 Summary:${NC}"
        echo "  Running processes: $running_count"
        echo "  Estimated completion: $(date -d '+2 hours' '+%H:%M')"
        echo ""
        
        # Show recent log activity
        if [[ -f "compression_detailed.log" ]]; then
            echo -e "${CYAN}📝 Recent Activity:${NC}"
            tail -5 compression_detailed.log 2>/dev/null | sed 's/^/  /' || echo "  No recent activity"
            echo ""
        fi
        
        echo -e "${BLUE}Press Ctrl+C to stop monitoring${NC}"
        echo "Next update in 30 seconds..."
        
        sleep 30
        ((cycle++))
    done
}

# Trap Ctrl+C
trap 'echo -e "\n${YELLOW}Monitoring stopped${NC}"; exit 0' INT

# Start monitoring
monitor_progress
