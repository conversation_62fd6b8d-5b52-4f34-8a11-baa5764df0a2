# 🎬 Video Compression Testing Suite

A comprehensive set of tools for testing video compression methods to find the optimal settings for Cloudinary upload while maintaining maximum visual quality.

## 📁 Files Overview

### Core Scripts

1. **`start_compression_test.sh`** - 🎯 **START HERE** - Interactive launcher
   - Easy-to-use menu interface
   - Automatically detects available videos
   - Guides you through test selection
   - Perfect for beginners

2. **`video_compression_test.sh`** - Complete compression testing suite
   - Tests all compression methods from the strategy document
   - Generates detailed analysis reports
   - Supports both quick and comprehensive testing modes

3. **`quick_compress_test.sh`** - Fast testing for immediate results
   - Tests 3 essential compression methods
   - Quick results in under 5 minutes
   - Perfect for single video testing

4. **`batch_compress_test.sh`** - Batch processing for all videos
   - Automatically finds and processes all videos in workspace
   - Generates comprehensive batch summary
   - Ideal for testing entire video collection

### Setup & Utilities

5. **`install_dependencies.sh`** - Dependency installer
   - Automatically detects your OS
   - Installs FFmpeg and required tools
   - Verifies encoder availability

### Documentation

6. **`压缩方案选择.md`** - Comprehensive compression strategy guide
7. **`README.md`** - This usage guide

## 🚀 Quick Start

### Step 1: Install Dependencies

**Automatic Installation (Recommended):**
```bash
./install_dependencies.sh
```

**Manual Installation:**
```bash
# Ubuntu/Debian
sudo apt-get update && sudo apt-get install ffmpeg bc

# CentOS/RHEL
sudo yum install epel-release && sudo yum install ffmpeg bc

# Fedora
sudo dnf install ffmpeg bc

# Arch Linux
sudo pacman -S ffmpeg bc

# macOS
brew install ffmpeg bc
```

### Step 2: Start Testing

**Interactive Mode (Recommended for beginners):**
```bash
./start_compression_test.sh
```

This will show you a menu to:
- Select which video to test
- Choose compression method (quick/comprehensive/essential)
- Get guided recommendations

## 📖 Usage Guide

### 1. Quick Test (Recommended for First Time)

Test a single video with essential compression methods:

```bash
./quick_compress_test.sh anniversary/anniversary.mp4
```

**Output**: 3 compressed versions with size comparison and recommendation.

### 2. Comprehensive Test

Test all compression methods for detailed analysis:

```bash
# Full test with all methods
./video_compression_test.sh anniversary/anniversary.mp4

# Quick mode (essential tests only)
./video_compression_test.sh --quick anniversary/anniversary.mp4

# Custom output directory and resolution
./video_compression_test.sh -o my_tests -r 1920:1080 video.mp4
```

**Output**: 
- Multiple compressed versions
- Detailed analysis report (`.md` file)
- Performance metrics and recommendations

### 3. Batch Processing

Test all videos in the workspace:

```bash
./batch_compress_test.sh
```

**Output**:
- Compressed version of each video
- Batch summary report
- Overall statistics and recommendations

## 📊 Understanding Results

### File Size Expectations

Based on your compression strategy document:

| Method | Expected Size | Quality | Compatibility |
|--------|---------------|---------|---------------|
| H.265 CRF 12 | 60-80MB | ⭐⭐⭐⭐⭐ | Modern browsers |
| H.265 CRF 14 | 50-65MB | ⭐⭐⭐⭐⭐ | Modern browsers |
| H.265 CRF 16 | 40-55MB | ⭐⭐⭐⭐ | Modern browsers |
| AV1 CRF 15 | 40-50MB | ⭐⭐⭐⭐⭐ | Latest browsers |
| H.264 CRF 18 | 80-120MB | ⭐⭐⭐⭐ | All browsers |

### Quality Scores

The scripts calculate quality scores based on:
- Compression ratio (higher is better)
- File size reduction
- Visual quality retention

### Recommendations

Scripts automatically recommend the best option based on:
- File size suitable for web delivery
- Quality retention
- Browser compatibility
- Cloudinary optimization

## 🎯 Cloudinary Optimization

### Recommended Settings

Based on testing results, the optimal settings for Cloudinary are typically:

1. **Primary Choice**: H.265 CRF 14
   - Excellent quality retention
   - Good compression ratio
   - Modern browser support

2. **Alternative**: H.265 CRF 16
   - Smaller file size
   - Still excellent quality
   - Better for bandwidth-limited scenarios

3. **Fallback**: H.264 CRF 18
   - Universal compatibility
   - Larger file size but guaranteed playback

### Upload Considerations

- Target file size: Under 100MB for optimal Cloudinary performance
- Resolution: 2560x1440 (configurable)
- Format: MP4 with H.265 or H.264
- Audio: Removed (`-an` flag) to focus on visual quality

## 📁 Output Structure

### Single Video Test
```
compression_tests/
├── video_h265_crf12.mp4
├── video_h265_crf14.mp4
├── video_h265_crf16.mp4
├── video_av1_crf15.mp4
├── video_compression_report.md
└── compression_detailed.log
```

### Batch Test
```
batch_compression_YYYYMMDD_HHMMSS/
├── anniversary/
│   └── anniversary_h265_crf14.mp4
├── home/
│   └── home_h265_crf14.mp4
├── batch_summary.md
└── results.csv
```

## 🔧 Advanced Usage

### Custom Resolution

```bash
./video_compression_test.sh -r 1920:1080 video.mp4
```

### Custom Output Directory

```bash
./video_compression_test.sh -o custom_output video.mp4
```

### Quick Mode Only

```bash
./video_compression_test.sh --quick video.mp4
```

## 📈 Performance Tips

1. **Use Quick Test First**: Start with `quick_compress_test.sh` to get immediate results
2. **Batch Processing**: Use `batch_compress_test.sh` for multiple videos
3. **Monitor Resources**: Compression is CPU-intensive, especially for H.265 and AV1
4. **Storage Space**: Ensure sufficient disk space (tests can generate multiple large files)

## 🐛 Troubleshooting

### Common Issues

1. **FFmpeg not found**
   ```bash
   sudo apt-get install ffmpeg
   ```

2. **BC calculator missing**
   ```bash
   sudo apt-get install bc
   ```

3. **Permission denied**
   ```bash
   chmod +x *.sh
   ```

4. **AV1/VP9 encoder not available**
   - These are optional encoders
   - Scripts will skip unavailable encoders automatically

### Log Files

Check detailed logs for debugging:
- `compression_detailed.log` - FFmpeg output and errors
- Script output shows real-time progress and issues

## 📞 Support

If you encounter issues:
1. Check the detailed log files
2. Verify all dependencies are installed
3. Ensure input video files are valid
4. Check available disk space

---

**Happy compressing! 🎬✨**
