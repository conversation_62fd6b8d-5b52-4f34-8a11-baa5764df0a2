#!/bin/bash

# 🔧 Dependency Installation Script
# Install required tools for video compression testing

set -e

# Color codes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_header() { echo -e "${PURPLE}========== $1 ==========${NC}"; }

# Detect OS
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$ID
        VERSION=$VERSION_ID
    elif [[ -f /etc/redhat-release ]]; then
        OS="centos"
    elif [[ -f /etc/debian_version ]]; then
        OS="debian"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    else
        OS="unknown"
    fi
    
    echo "$OS"
}

# Check if running as root (for system package installation)
check_root() {
    if [[ $EUID -eq 0 ]]; then
        return 0
    else
        return 1
    fi
}

# Install dependencies based on OS
install_dependencies() {
    local os=$(detect_os)
    
    print_header "Installing Dependencies for $os"
    
    case $os in
        "ubuntu"|"debian")
            print_info "Updating package list..."
            if check_root; then
                apt-get update
                print_info "Installing FFmpeg and bc..."
                apt-get install -y ffmpeg bc
            else
                print_info "Need sudo privileges to install system packages..."
                sudo apt-get update
                sudo apt-get install -y ffmpeg bc
            fi
            ;;
        "centos"|"rhel"|"fedora")
            if command -v dnf >/dev/null 2>&1; then
                print_info "Installing with dnf..."
                if check_root; then
                    dnf install -y ffmpeg bc
                else
                    sudo dnf install -y ffmpeg bc
                fi
            elif command -v yum >/dev/null 2>&1; then
                print_info "Installing with yum..."
                # Enable EPEL repository for FFmpeg
                if check_root; then
                    yum install -y epel-release
                    yum install -y ffmpeg bc
                else
                    sudo yum install -y epel-release
                    sudo yum install -y ffmpeg bc
                fi
            else
                print_error "No package manager found (dnf/yum)"
                return 1
            fi
            ;;
        "arch")
            print_info "Installing with pacman..."
            if check_root; then
                pacman -S --noconfirm ffmpeg bc
            else
                sudo pacman -S --noconfirm ffmpeg bc
            fi
            ;;
        "macos")
            if command -v brew >/dev/null 2>&1; then
                print_info "Installing with Homebrew..."
                brew install ffmpeg bc
            else
                print_error "Homebrew not found. Please install Homebrew first:"
                echo "  /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
                return 1
            fi
            ;;
        *)
            print_error "Unsupported operating system: $os"
            print_info "Please install FFmpeg and bc manually:"
            echo "  - FFmpeg: https://ffmpeg.org/download.html"
            echo "  - bc: Basic calculator (usually available in system repositories)"
            return 1
            ;;
    esac
}

# Check current installation status
check_dependencies() {
    print_header "Checking Dependencies"
    
    local all_good=true
    
    # Check FFmpeg
    if command -v ffmpeg >/dev/null 2>&1; then
        local ffmpeg_version=$(ffmpeg -version 2>/dev/null | head -n1 | cut -d' ' -f3)
        print_success "FFmpeg found: version $ffmpeg_version"
        
        # Check for important encoders
        local encoders=$(ffmpeg -encoders 2>/dev/null)
        
        if echo "$encoders" | grep -q libx264; then
            print_success "  ✓ H.264 encoder (libx264) available"
        else
            print_warning "  ⚠ H.264 encoder (libx264) not found"
        fi
        
        if echo "$encoders" | grep -q libx265; then
            print_success "  ✓ H.265 encoder (libx265) available"
        else
            print_warning "  ⚠ H.265 encoder (libx265) not found"
        fi
        
        if echo "$encoders" | grep -q libaom-av1; then
            print_success "  ✓ AV1 encoder (libaom-av1) available"
        else
            print_info "  ℹ AV1 encoder (libaom-av1) not available (optional)"
        fi
        
        if echo "$encoders" | grep -q libvpx-vp9; then
            print_success "  ✓ VP9 encoder (libvpx-vp9) available"
        else
            print_info "  ℹ VP9 encoder (libvpx-vp9) not available (optional)"
        fi
        
    else
        print_error "FFmpeg not found"
        all_good=false
    fi
    
    # Check FFprobe
    if command -v ffprobe >/dev/null 2>&1; then
        print_success "FFprobe found"
    else
        print_error "FFprobe not found"
        all_good=false
    fi
    
    # Check bc
    if command -v bc >/dev/null 2>&1; then
        print_success "bc (calculator) found"
    else
        print_error "bc (calculator) not found"
        all_good=false
    fi
    
    echo ""
    
    if [[ "$all_good" == true ]]; then
        print_success "All dependencies are installed and ready!"
        return 0
    else
        print_warning "Some dependencies are missing."
        return 1
    fi
}

# Main function
main() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              🔧 Dependency Installation Script              ║"
    echo "║                                                              ║"
    echo "║         Install FFmpeg and bc for video compression         ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
    
    # Check current status
    if check_dependencies; then
        print_info "All dependencies are already installed. You're ready to go!"
        exit 0
    fi
    
    echo ""
    print_info "Some dependencies need to be installed."
    
    # Ask user if they want to install
    echo -n "Do you want to install missing dependencies? (y/N): "
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo ""
        if install_dependencies; then
            echo ""
            print_success "Installation completed!"
            
            # Verify installation
            echo ""
            if check_dependencies; then
                print_success "🎉 All dependencies are now installed and working!"
                echo ""
                print_info "You can now run the compression tests:"
                echo "  ./start_compression_test.sh    # Interactive launcher"
                echo "  ./quick_compress_test.sh video.mp4    # Quick test"
                echo "  ./batch_compress_test.sh       # Test all videos"
            else
                print_error "Installation completed but some issues remain."
                print_info "Please check the error messages above."
            fi
        else
            print_error "Installation failed. Please install dependencies manually."
        fi
    else
        print_info "Installation cancelled."
        echo ""
        print_info "To install manually:"
        echo "  Ubuntu/Debian: sudo apt-get install ffmpeg bc"
        echo "  CentOS/RHEL: sudo yum install epel-release && sudo yum install ffmpeg bc"
        echo "  Fedora: sudo dnf install ffmpeg bc"
        echo "  Arch: sudo pacman -S ffmpeg bc"
        echo "  macOS: brew install ffmpeg bc"
    fi
}

# Run main function
main "$@"
