#!/bin/bash

# 🎬 全方案视频压缩脚本 - 基于压缩方案选择.md
# 使用双线程和原分辨率进行所有方案的压缩测试

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 创建输出目录
create_output_dirs() {
    local video_name=$1
    mkdir -p "compressed/${video_name}"/{h265_crf12,h265_crf14,h265_2pass,h265_hybrid,av1_crf15,vp9_crf18,h264_crf16}
    log_info "Created output directories for $video_name"
}

# 获取视频分辨率
get_resolution() {
    local input_file=$1
    ffprobe -v quiet -select_streams v:0 -show_entries stream=width,height -of csv=p=0 "$input_file"
}

# 获取格式化的分辨率 (width:height)
get_formatted_resolution() {
    local input_file=$1
    local resolution=$(get_resolution "$input_file")
    echo "$resolution" | tr ',' ':'
}

# 方案1: H.265 CRF 12 接近无损
compress_h265_crf12() {
    local input_file=$1
    local video_name=$2
    local resolution=$3
    local formatted_resolution=$4
    local output_file="compressed/${video_name}/h265_crf12/${video_name}_h265_crf12.mp4"

    log_info "开始 H.265 CRF 12 压缩: $video_name (分辨率: $resolution)"

    ffmpeg -i "$input_file" \
        -c:v libx265 \
        -crf 12 \
        -preset slower \
        -profile:v main \
        -level 5.1 \
        -x265-params "me=umh:subme=3:ref=5:bframes=8:rd=3:psy-rd=1.0:aq-mode=3" \
        -vf "scale=${formatted_resolution}:flags=lanczos:param0=3" \
        -colorspace bt709 \
        -color_primaries bt709 \
        -color_trc bt709 \
        -an \
        -movflags +faststart \
        -pix_fmt yuv420p \
        -threads 2 \
        -y "$output_file" 2>&1 | tee "logs/${video_name}_h265_crf12.log"

    if [ $? -eq 0 ]; then
        log_success "H.265 CRF 12 压缩完成: $output_file"
        ls -lh "$output_file"
    else
        log_error "H.265 CRF 12 压缩失败: $video_name"
    fi
}

# 方案2: H.265 CRF 14 推荐方案
compress_h265_crf14() {
    local input_file=$1
    local video_name=$2
    local resolution=$3
    local formatted_resolution=$4
    local output_file="compressed/${video_name}/h265_crf14/${video_name}_h265_crf14.mp4"

    log_info "开始 H.265 CRF 14 压缩: $video_name (分辨率: $resolution)"

    ffmpeg -i "$input_file" \
        -c:v libx265 \
        -crf 14 \
        -preset slower \
        -profile:v main \
        -level 5.1 \
        -x265-params "me=umh:subme=3:ref=5:bframes=8:b-adapt=2:rd=3:psy-rd=1.0:psy-rdoq=1.0:aq-mode=3:aq-strength=0.8:deblock=1,1" \
        -vf "scale=${formatted_resolution}:flags=lanczos:param0=3" \
        -colorspace bt709 \
        -color_primaries bt709 \
        -color_trc bt709 \
        -an \
        -movflags +faststart \
        -pix_fmt yuv420p \
        -threads 2 \
        -y "$output_file" 2>&1 | tee "logs/${video_name}_h265_crf14.log"

    if [ $? -eq 0 ]; then
        log_success "H.265 CRF 14 压缩完成: $output_file"
        ls -lh "$output_file"
    else
        log_error "H.265 CRF 14 压缩失败: $video_name"
    fi
}

# 方案3: H.265 两遍编码
compress_h265_2pass() {
    local input_file=$1
    local video_name=$2
    local resolution=$3
    local formatted_resolution=$4
    local output_file="compressed/${video_name}/h265_2pass/${video_name}_h265_2pass.mp4"
    local bitrate="9000k"

    log_info "开始 H.265 两遍编码: $video_name (分辨率: $resolution, 码率: $bitrate)"

    # 第一遍
    log_info "第一遍分析: $video_name"
    ffmpeg -i "$input_file" \
        -c:v libx265 \
        -b:v "$bitrate" \
        -preset slower \
        -pass 1 \
        -threads 2 \
        -f null /dev/null 2>&1 | tee "logs/${video_name}_h265_2pass_pass1.log"

    if [ $? -eq 0 ]; then
        log_info "第一遍完成，开始第二遍编码: $video_name"
        # 第二遍
        ffmpeg -i "$input_file" \
            -c:v libx265 \
            -b:v "$bitrate" \
            -preset slower \
            -pass 2 \
            -profile:v main \
            -level 5.1 \
            -vf "scale=${formatted_resolution}:flags=lanczos" \
            -an \
            -movflags +faststart \
            -threads 2 \
            -y "$output_file" 2>&1 | tee "logs/${video_name}_h265_2pass_pass2.log"

        if [ $? -eq 0 ]; then
            log_success "H.265 两遍编码完成: $output_file"
            ls -lh "$output_file"
            # 清理临时文件
            rm -f ffmpeg2pass-*.log
        else
            log_error "H.265 两遍编码第二遍失败: $video_name"
        fi
    else
        log_error "H.265 两遍编码第一遍失败: $video_name"
    fi
}

# 方案4: H.265 混合编码策略
compress_h265_hybrid() {
    local input_file=$1
    local video_name=$2
    local resolution=$3
    local formatted_resolution=$4
    local output_file="compressed/${video_name}/h265_hybrid/${video_name}_h265_hybrid.mp4"

    log_info "开始 H.265 混合编码: $video_name (分辨率: $resolution)"

    ffmpeg -i "$input_file" \
        -c:v libx265 \
        -crf 14 \
        -preset slower \
        -x265-params "me=umh:subme=3:ref=6:bframes=8:b-adapt=2:rd=4:psy-rd=1.0:psy-rdoq=2.0:aq-mode=3:aq-strength=0.8:qcomp=0.6:deblock=1,1:sao=1:limit-sao=1" \
        -vf "hqdn3d=1:0.5:1:0.5,scale=${formatted_resolution}:flags=lanczos:param0=3" \
        -an \
        -threads 2 \
        -y "$output_file" 2>&1 | tee "logs/${video_name}_h265_hybrid.log"

    if [ $? -eq 0 ]; then
        log_success "H.265 混合编码完成: $output_file"
        ls -lh "$output_file"
    else
        log_error "H.265 混合编码失败: $video_name"
    fi
}

# 方案5: AV1 最新技术
compress_av1() {
    local input_file=$1
    local video_name=$2
    local resolution=$3
    local formatted_resolution=$4
    local output_file="compressed/${video_name}/av1_crf15/${video_name}_av1_crf15.mp4"

    log_info "开始 AV1 CRF 15 压缩: $video_name (分辨率: $resolution)"

    ffmpeg -i "$input_file" \
        -c:v libaom-av1 \
        -crf 15 \
        -cpu-used 1 \
        -row-mt 1 \
        -tiles 4x2 \
        -g 240 \
        -keyint_min 23 \
        -aq-mode 1 \
        -tune-content 0 \
        -vf "scale=${formatted_resolution}:flags=lanczos" \
        -an \
        -threads 2 \
        -y "$output_file" 2>&1 | tee "logs/${video_name}_av1_crf15.log"

    if [ $? -eq 0 ]; then
        log_success "AV1 CRF 15 压缩完成: $output_file"
        ls -lh "$output_file"
    else
        log_error "AV1 CRF 15 压缩失败: $video_name"
    fi
}

# 方案6: VP9 优化
compress_vp9() {
    local input_file=$1
    local video_name=$2
    local resolution=$3
    local formatted_resolution=$4
    local output_file="compressed/${video_name}/vp9_crf18/${video_name}_vp9_crf18.webm"

    log_info "开始 VP9 CRF 18 压缩: $video_name (分辨率: $resolution)"

    ffmpeg -i "$input_file" \
        -c:v libvpx-vp9 \
        -crf 18 \
        -b:v 0 \
        -cpu-used 2 \
        -row-mt 1 \
        -tile-columns 2 \
        -frame-parallel 1 \
        -vf "scale=${formatted_resolution}:flags=lanczos" \
        -an \
        -threads 2 \
        -y "$output_file" 2>&1 | tee "logs/${video_name}_vp9_crf18.log"

    if [ $? -eq 0 ]; then
        log_success "VP9 CRF 18 压缩完成: $output_file"
        ls -lh "$output_file"
    else
        log_error "VP9 CRF 18 压缩失败: $video_name"
    fi
}

# 方案7: H.264 传统标准
compress_h264() {
    local input_file=$1
    local video_name=$2
    local resolution=$3
    local formatted_resolution=$4
    local output_file="compressed/${video_name}/h264_crf16/${video_name}_h264_crf16.mp4"

    log_info "开始 H.264 CRF 16 压缩: $video_name (分辨率: $resolution)"

    ffmpeg -i "$input_file" \
        -c:v libx264 \
        -crf 16 \
        -preset veryslow \
        -profile:v high \
        -level 5.1 \
        -vf "scale=${formatted_resolution}:flags=lanczos" \
        -an \
        -movflags +faststart \
        -pix_fmt yuv420p \
        -threads 2 \
        -y "$output_file" 2>&1 | tee "logs/${video_name}_h264_crf16.log"

    if [ $? -eq 0 ]; then
        log_success "H.264 CRF 16 压缩完成: $output_file"
        ls -lh "$output_file"
    else
        log_error "H.264 CRF 16 压缩失败: $video_name"
    fi
}

# 处理单个视频的所有方案
process_video() {
    local input_file=$1
    local video_name=$(basename "$input_file" .mp4)
    local resolution=$(get_resolution "$input_file")
    local formatted_resolution=$(get_formatted_resolution "$input_file")

    log_info "开始处理视频: $video_name (分辨率: $resolution)"

    # 创建输出目录
    create_output_dirs "$video_name"

    # 记录开始时间
    local start_time=$(date +%s)

    # 执行所有压缩方案
    log_info "=== 开始执行所有压缩方案 ==="

    # 方案1: H.265 CRF 12 (接近无损)
    compress_h265_crf12 "$input_file" "$video_name" "$resolution" "$formatted_resolution"

    # 方案2: H.265 CRF 14 (推荐)
    compress_h265_crf14 "$input_file" "$video_name" "$resolution" "$formatted_resolution"

    # 方案3: H.265 两遍编码
    compress_h265_2pass "$input_file" "$video_name" "$resolution" "$formatted_resolution"

    # 方案4: H.265 混合编码
    compress_h265_hybrid "$input_file" "$video_name" "$resolution" "$formatted_resolution"

    # 方案5: AV1 (最新技术)
    compress_av1 "$input_file" "$video_name" "$resolution" "$formatted_resolution"

    # 方案6: VP9 优化
    compress_vp9 "$input_file" "$video_name" "$resolution" "$formatted_resolution"

    # 方案7: H.264 传统
    compress_h264 "$input_file" "$video_name" "$resolution" "$formatted_resolution"

    # 计算总耗时
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local hours=$((duration / 3600))
    local minutes=$(((duration % 3600) / 60))
    local seconds=$((duration % 60))

    log_success "视频 $video_name 所有方案压缩完成！总耗时: ${hours}h ${minutes}m ${seconds}s"

    # 生成压缩报告
    generate_compression_report "$video_name" "$input_file"
}

# 生成压缩报告
generate_compression_report() {
    local video_name=$1
    local input_file=$2
    local report_file="compressed/${video_name}/${video_name}_compression_report.md"

    log_info "生成压缩报告: $report_file"

    # 获取原始文件信息
    local original_size=$(stat -c%s "$input_file")
    local original_size_mb=$((original_size / 1024 / 1024))

    cat > "$report_file" << EOF
# 📊 视频压缩报告 - $video_name

## 原始文件信息
- **文件名**: $input_file
- **文件大小**: ${original_size_mb} MB
- **分辨率**: $(get_resolution "$input_file")
- **压缩时间**: $(date '+%Y-%m-%d %H:%M:%S')

## 压缩结果对比

| 方案 | 编码器 | 参数 | 文件大小 | 压缩比 | 画质等级 |
|------|--------|------|----------|--------|----------|
EOF

    # 添加各方案的结果
    for method in h265_crf12 h265_crf14 h265_2pass h265_hybrid av1_crf15 h264_crf16; do
        local output_dir="compressed/${video_name}/${method}"
        local output_file=$(find "$output_dir" -name "*.mp4" -o -name "*.webm" 2>/dev/null | head -1)

        if [ -f "$output_file" ]; then
            local compressed_size=$(stat -c%s "$output_file")
            local compressed_size_mb=$((compressed_size / 1024 / 1024))
            local compression_ratio=$(echo "scale=1; $compressed_size * 100 / $original_size" | bc)

            case $method in
                h265_crf12) echo "| H.265 CRF 12 | libx265 | CRF 12 | ${compressed_size_mb} MB | ${compression_ratio}% | ⭐⭐⭐⭐⭐ |" >> "$report_file" ;;
                h265_crf14) echo "| H.265 CRF 14 | libx265 | CRF 14 | ${compressed_size_mb} MB | ${compression_ratio}% | ⭐⭐⭐⭐⭐ |" >> "$report_file" ;;
                h265_2pass) echo "| H.265 两遍 | libx265 | 9000k | ${compressed_size_mb} MB | ${compression_ratio}% | ⭐⭐⭐⭐⭐ |" >> "$report_file" ;;
                h265_hybrid) echo "| H.265 混合 | libx265 | CRF 14+滤镜 | ${compressed_size_mb} MB | ${compression_ratio}% | ⭐⭐⭐⭐⭐ |" >> "$report_file" ;;
                av1_crf15) echo "| AV1 CRF 15 | libaom-av1 | CRF 15 | ${compressed_size_mb} MB | ${compression_ratio}% | ⭐⭐⭐⭐⭐ |" >> "$report_file" ;;
                h264_crf16) echo "| H.264 CRF 16 | libx264 | CRF 16 | ${compressed_size_mb} MB | ${compression_ratio}% | ⭐⭐⭐⭐ |" >> "$report_file" ;;
            esac
        else
            case $method in
                h265_crf12) echo "| H.265 CRF 12 | libx265 | CRF 12 | 压缩失败 | - | - |" >> "$report_file" ;;
                h265_crf14) echo "| H.265 CRF 14 | libx265 | CRF 14 | 压缩失败 | - | - |" >> "$report_file" ;;
                h265_2pass) echo "| H.265 两遍 | libx265 | 9000k | 压缩失败 | - | - |" >> "$report_file" ;;
                h265_hybrid) echo "| H.265 混合 | libx265 | CRF 14+滤镜 | 压缩失败 | - | - |" >> "$report_file" ;;
                av1_crf15) echo "| AV1 CRF 15 | libaom-av1 | CRF 15 | 压缩失败 | - | - |" >> "$report_file" ;;
                h264_crf16) echo "| H.264 CRF 16 | libx264 | CRF 16 | 压缩失败 | - | - |" >> "$report_file" ;;
            esac
        fi
    done

    # VP9 单独处理 (webm格式)
    local vp9_file="compressed/${video_name}/vp9_crf18/${video_name}_vp9_crf18.webm"
    if [ -f "$vp9_file" ]; then
        local vp9_size=$(stat -c%s "$vp9_file")
        local vp9_size_mb=$((vp9_size / 1024 / 1024))
        local vp9_ratio=$(echo "scale=1; $vp9_size * 100 / $original_size" | bc)
        echo "| VP9 CRF 18 | libvpx-vp9 | CRF 18 | ${vp9_size_mb} MB | ${vp9_ratio}% | ⭐⭐⭐⭐ |" >> "$report_file"
    else
        echo "| VP9 CRF 18 | libvpx-vp9 | CRF 18 | 压缩失败 | - | - |" >> "$report_file"
    fi

    cat >> "$report_file" << EOF

## 推荐方案

根据压缩方案选择.md的建议：

1. **最佳画质保持**: H.265 CRF 12-14
2. **最优压缩效率**: AV1 CRF 15 (需要较新浏览器)
3. **兼容性最佳**: H.264 CRF 16
4. **开源方案**: VP9 CRF 18

## 技术参数

- **线程数**: 2 (双线程)
- **分辨率**: 保持原始分辨率
- **音频**: 已移除 (-an)
- **像素格式**: yuv420p
- **色彩空间**: bt709

生成时间: $(date '+%Y-%m-%d %H:%M:%S')
EOF

    log_success "压缩报告已生成: $report_file"
}

# 主函数
main() {
    log_info "🎬 开始全方案视频压缩 - 基于压缩方案选择.md"
    log_info "使用双线程和原分辨率进行压缩"

    # 创建必要目录
    mkdir -p compressed logs

    # 检查依赖
    if ! command -v ffmpeg &> /dev/null; then
        log_error "ffmpeg 未安装，请先安装 ffmpeg"
        exit 1
    fi

    if ! command -v bc &> /dev/null; then
        log_error "bc 未安装，请先安装 bc (用于计算压缩比)"
        exit 1
    fi

    # 定义要处理的视频文件
    local videos=(
        "anniversary/anniversary.mp4"
        "home/home.mp4"
        "meetings/meetings.mp4"
        "memorial/memorial.mp4"
        "together-days/together-days.mp4"
    )

    local total_start_time=$(date +%s)

    # 处理每个视频
    for video in "${videos[@]}"; do
        if [ -f "$video" ]; then
            process_video "$video"
        else
            log_warning "视频文件不存在: $video"
        fi
    done

    # 计算总耗时
    local total_end_time=$(date +%s)
    local total_duration=$((total_end_time - total_start_time))
    local total_hours=$((total_duration / 3600))
    local total_minutes=$(((total_duration % 3600) / 60))
    local total_seconds=$((total_duration % 60))

    log_success "🎉 所有视频的全方案压缩完成！"
    log_success "总耗时: ${total_hours}h ${total_minutes}m ${total_seconds}s"

    # 生成总体报告
    generate_summary_report
}

# 生成总体报告
generate_summary_report() {
    local summary_file="compressed/COMPRESSION_SUMMARY.md"

    log_info "生成总体压缩报告: $summary_file"

    cat > "$summary_file" << EOF
# 🎬 全方案视频压缩总结报告

## 压缩完成时间
$(date '+%Y-%m-%d %H:%M:%S')

## 处理的视频文件
1. anniversary/anniversary.mp4 (2560x1440)
2. home/home.mp4 (2560x1440)
3. meetings/meetings.mp4 (3840x2160)
4. memorial/memorial.mp4 (3840x2160)
5. together-days/together-days.mp4 (2560x1440)

## 实施的压缩方案
1. **H.265 CRF 12** - 接近无损方案
2. **H.265 CRF 14** - 推荐方案
3. **H.265 两遍编码** - 精确大小控制
4. **H.265 混合编码** - 最佳质量/大小平衡
5. **AV1 CRF 15** - 最新技术方案
6. **VP9 CRF 18** - 开源方案
7. **H.264 CRF 16** - 传统兼容方案

## 技术规格
- **线程数**: 2 (双线程)
- **分辨率**: 保持原始分辨率
- **音频处理**: 移除音频轨道 (-an)
- **像素格式**: yuv420p
- **色彩空间**: bt709

## 输出目录结构
\`\`\`
compressed/
├── anniversary/
│   ├── h265_crf12/
│   ├── h265_crf14/
│   ├── h265_2pass/
│   ├── h265_hybrid/
│   ├── av1_crf15/
│   ├── vp9_crf18/
│   ├── h264_crf16/
│   └── anniversary_compression_report.md
├── home/
├── meetings/
├── memorial/
├── together-days/
└── COMPRESSION_SUMMARY.md (本文件)
\`\`\`

## 查看详细报告
每个视频的详细压缩报告请查看对应目录下的 \`*_compression_report.md\` 文件。

基于《压缩方案选择.md》实施 - $(date '+%Y-%m-%d %H:%M:%S')
EOF

    log_success "总体压缩报告已生成: $summary_file"
}

# 执行主函数
main "$@"
