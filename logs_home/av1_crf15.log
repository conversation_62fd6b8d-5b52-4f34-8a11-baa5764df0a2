ffmpeg version 4.4.2-0ubuntu0.22.04.1 Copyright (c) 2000-2021 the FFmpeg developers
  built with gcc 11 (Ubuntu 11.2.0-19ubuntu1)
  configuration: --prefix=/usr --extra-version=0ubuntu0.22.04.1 --toolchain=hardened --libdir=/usr/lib/x86_64-linux-gnu --incdir=/usr/include/x86_64-linux-gnu --arch=amd64 --enable-gpl --disable-stripping --enable-gnutls --enable-ladspa --enable-libaom --enable-libass --enable-libbluray --enable-libbs2b --enable-libcaca --enable-libcdio --enable-libcodec2 --enable-libdav1d --enable-libflite --enable-libfontconfig --enable-libfreetype --enable-libfribidi --enable-libgme --enable-libgsm --enable-libjack --enable-libmp3lame --enable-libmysofa --enable-libopenjpeg --enable-libopenmpt --enable-libopus --enable-libpulse --enable-librabbitmq --enable-librubberband --enable-libshine --enable-libsnappy --enable-libsoxr --enable-libspeex --enable-libsrt --enable-libssh --enable-libtheora --enable-libtwolame --enable-libvidstab --enable-libvorbis --enable-libvpx --enable-libwebp --enable-libx265 --enable-libxml2 --enable-libxvid --enable-libzimg --enable-libzmq --enable-libzvbi --enable-lv2 --enable-omx --enable-openal --enable-opencl --enable-opengl --enable-sdl2 --enable-pocketsphinx --enable-librsvg --enable-libmfx --enable-libdc1394 --enable-libdrm --enable-libiec61883 --enable-chromaprint --enable-frei0r --enable-libx264 --enable-shared
  libavutil      56. 70.100 / 56. 70.100
  libavcodec     58.134.100 / 58.134.100
  libavformat    58. 76.100 / 58. 76.100
  libavdevice    58. 13.100 / 58. 13.100
  libavfilter     7.110.100 /  7.110.100
  libswscale      5.  9.100 /  5.  9.100
  libswresample   3.  9.100 /  3.  9.100
  libpostproc    55.  9.100 / 55.  9.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'home/home.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    title           : Video file enhanced by Problembo.com service
    encoder         : Lavf61.7.100
  Duration: 00:00:15.02, start: 0.000000, bitrate: 34931 kb/s
  Stream #0:0(eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p, 2560x1440 [SAR 1:1 DAR 16:9], 34858 kb/s, 59.93 fps, 60 tbr, 15360 tbn, 120 tbc (default)
    Metadata:
      handler_name    : ?Mainconcept Video Media Handler
      vendor_id       : [0][0][0][0]
      encoder         : Lavc61.22.100 libvpx-vp9
  Stream #0:1(und): Audio: vorbis (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 98 kb/s (default)
    Metadata:
      handler_name    : SoundHandler
      vendor_id       : [0][0][0][0]
Codec AVOption tune-content (Tune content type) specified for output file #0 (compressed_home/av1_crf15/home_av1_crf15.mp4) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> av1 (libaom-av1))
Press [q] to stop, [?] for help
[libaom-av1 @ 0x59bb43e62300] v3.3.0
Output #0, mp4, to 'compressed_home/av1_crf15/home_av1_crf15.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    title           : Video file enhanced by Problembo.com service
    encoder         : Lavf58.76.100
  Stream #0:0(eng): Video: av1 (av01 / 0x31307661), yuv420p(progressive), 2560x1440 [SAR 1:1 DAR 16:9], q=2-31, 60 fps, 15360 tbn (default)
    Metadata:
      handler_name    : ?Mainconcept Video Media Handler
      vendor_id       : [0][0][0][0]
      encoder         : Lavc58.134.100 libaom-av1
    Side data:
      cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    1 fps=0.0 q=0.0 size=       0kB time=00:00:00.00 bitrate=N/A speed=   0x    
frame=    2 fps=0.0 q=0.0 size=       0kB time=00:00:00.00 bitrate=N/A speed=   0x    
frame=   12 fps=9.8 q=0.0 size=       0kB time=00:00:00.00 bitrate=N/A speed=   0x    
frame=   22 fps= 12 q=0.0 size=       0kB time=00:00:00.00 bitrate=N/A speed=   0x    
frame=   33 fps= 14 q=0.0 size=       0kB time=00:00:00.00 bitrate=N/A speed=   0x    
frame=   36 fps=0.4 q=0.0 size=     768kB time=00:00:00.01 bitrate=376036.3kbits/s speed=0.000171x    
frame=   37 fps=0.2 q=0.0 size=     768kB time=00:00:00.03 bitrate=188389.7kbits/s speed=0.000148x    
frame=   39 fps=0.2 q=0.0 size=     768kB time=00:00:00.06 bitrate=94285.2kbits/s speed=0.000283x    
frame=   41 fps=0.1 q=0.0 size=     768kB time=00:00:00.10 bitrate=62877.5kbits/s speed=0.000351x    
frame=   43 fps=0.1 q=0.0 size=     768kB time=00:00:00.13 bitrate=47165.9kbits/s speed=0.000443x    
frame=   45 fps=0.1 q=0.0 size=     768kB time=00:00:00.16 bitrate=37736.2kbits/s speed=0.000386x    
frame=   47 fps=0.1 q=0.0 size=     768kB time=00:00:00.20 bitrate=31449.0kbits/s speed=0.000443x    
frame=   49 fps=0.1 q=0.0 size=     768kB time=00:00:00.23 bitrate=26957.6kbits/s speed=0.000448x    
frame=   51 fps=0.1 q=0.0 size=     768kB time=00:00:00.26 bitrate=23588.6kbits/s speed=0.000492x    
