#!/bin/bash

# 🚀 Quick Video Compression Test
# Fast testing for optimal Cloudinary upload settings

set -e

# Color codes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Get file size in MB
get_size_mb() {
    local file="$1"
    if [[ -f "$file" ]]; then
        local size_bytes=$(stat -c%s "$file")
        echo "scale=2; $size_bytes / 1024 / 1024" | bc
    else
        echo "0"
    fi
}

# Quick compression test
quick_test() {
    local input="$1"
    local output_dir="quick_test_$(date +%Y%m%d_%H%M%S)"
    local base_name=$(basename "$input" .mp4)
    
    mkdir -p "$output_dir"
    
    print_info "Input: $input"
    print_info "Original size: $(get_size_mb "$input") MB"
    echo ""
    
    # Test 1: H.265 CRF 14 (Recommended for Cloudinary)
    print_info "Testing H.265 CRF 14 (Recommended)..."
    local output1="$output_dir/${base_name}_h265_crf14.mp4"
    
    ffmpeg -y -i "$input" \
        -c:v libx265 \
        -crf 14 \
        -preset medium \
        -profile:v main \
        -level 5.1 \
        -vf "scale=2560:1440:flags=lanczos" \
        -an \
        -movflags +faststart \
        -pix_fmt yuv420p \
        "$output1" \
        -loglevel error -stats
    
    local size1=$(get_size_mb "$output1")
    print_success "H.265 CRF 14: ${size1} MB"
    
    # Test 2: H.265 CRF 16 (More compressed)
    print_info "Testing H.265 CRF 16 (More compressed)..."
    local output2="$output_dir/${base_name}_h265_crf16.mp4"
    
    ffmpeg -y -i "$input" \
        -c:v libx265 \
        -crf 16 \
        -preset medium \
        -profile:v main \
        -level 5.1 \
        -vf "scale=2560:1440:flags=lanczos" \
        -an \
        -movflags +faststart \
        -pix_fmt yuv420p \
        "$output2" \
        -loglevel error -stats
    
    local size2=$(get_size_mb "$output2")
    print_success "H.265 CRF 16: ${size2} MB"
    
    # Test 3: H.264 CRF 18 (Most compatible)
    print_info "Testing H.264 CRF 18 (Most compatible)..."
    local output3="$output_dir/${base_name}_h264_crf18.mp4"
    
    ffmpeg -y -i "$input" \
        -c:v libx264 \
        -crf 18 \
        -preset medium \
        -profile:v high \
        -level 5.1 \
        -vf "scale=2560:1440:flags=lanczos" \
        -an \
        -movflags +faststart \
        -pix_fmt yuv420p \
        "$output3" \
        -loglevel error -stats
    
    local size3=$(get_size_mb "$output3")
    print_success "H.264 CRF 18: ${size3} MB"
    
    # Summary
    echo ""
    echo "========================================="
    echo "🎯 QUICK TEST RESULTS"
    echo "========================================="
    echo "Original:        $(get_size_mb "$input") MB"
    echo "H.265 CRF 14:    ${size1} MB (Recommended)"
    echo "H.265 CRF 16:    ${size2} MB (Smaller)"
    echo "H.264 CRF 18:    ${size3} MB (Compatible)"
    echo ""
    echo "📁 Output directory: $output_dir"
    echo ""
    
    # Recommendation
    local original_size=$(get_size_mb "$input")
    local reduction1=$(echo "scale=1; (1 - $size1 / $original_size) * 100" | bc)
    local reduction2=$(echo "scale=1; (1 - $size2 / $original_size) * 100" | bc)
    
    echo "🏆 RECOMMENDATION FOR CLOUDINARY:"
    if (( $(echo "$size1 < 100" | bc -l) )); then
        echo "✅ Use H.265 CRF 14 - Best quality, ${reduction1}% size reduction"
    elif (( $(echo "$size2 < 100" | bc -l) )); then
        echo "✅ Use H.265 CRF 16 - Good quality, ${reduction2}% size reduction"
    else
        echo "⚠️  Consider H.265 CRF 18 for smaller files"
    fi
    echo ""
}

# Main
if [[ $# -eq 0 ]]; then
    echo "Usage: $0 <video_file>"
    echo "Example: $0 anniversary/anniversary.mp4"
    exit 1
fi

if [[ ! -f "$1" ]]; then
    print_error "File not found: $1"
    exit 1
fi

# Check dependencies
if ! command -v ffmpeg >/dev/null 2>&1; then
    print_error "ffmpeg not found. Please install ffmpeg."
    exit 1
fi

if ! command -v bc >/dev/null 2>&1; then
    print_error "bc not found. Please install bc calculator."
    exit 1
fi

quick_test "$1"
