#!/bin/bash

# 📦 Batch Video Compression Test
# Test all videos in the workspace for optimal Cloudinary settings

set -e

# Color codes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_header() { echo -e "${PURPLE}========== $1 ==========${NC}"; }

# Configuration
BATCH_OUTPUT_DIR="batch_compression_$(date +%Y%m%d_%H%M%S)"
SUMMARY_FILE="$BATCH_OUTPUT_DIR/batch_summary.md"

# Get file size in MB
get_size_mb() {
    local file="$1"
    if [[ -f "$file" ]]; then
        local size_bytes=$(stat -c%s "$file")
        echo "scale=2; $size_bytes / 1024 / 1024" | bc
    else
        echo "0"
    fi
}

# Test single video with optimal settings
test_single_video() {
    local input="$1"
    local video_name=$(basename "$(dirname "$input")")
    local base_name=$(basename "$input" .mp4)
    local video_output_dir="$BATCH_OUTPUT_DIR/$video_name"
    
    mkdir -p "$video_output_dir"
    
    print_header "Testing: $video_name"
    print_info "Input: $input"
    
    local original_size=$(get_size_mb "$input")
    print_info "Original size: ${original_size} MB"
    
    # Get video info
    local duration=$(ffprobe -v quiet -show_entries format=duration -of csv=p=0 "$input" 2>/dev/null || echo "unknown")
    local resolution=$(ffprobe -v quiet -select_streams v:0 -show_entries stream=width,height -of csv=s=x:p=0 "$input" 2>/dev/null || echo "unknown")
    
    print_info "Duration: ${duration}s, Resolution: $resolution"
    echo ""
    
    # Test H.265 CRF 14 (Recommended)
    print_info "Compressing with H.265 CRF 14..."
    local output_h265="$video_output_dir/${base_name}_h265_crf14.mp4"
    local start_time=$(date +%s)
    
    if ffmpeg -y -i "$input" \
        -c:v libx265 \
        -crf 14 \
        -preset medium \
        -profile:v main \
        -level 5.1 \
        -vf "scale=2560:1440:flags=lanczos" \
        -an \
        -movflags +faststart \
        -pix_fmt yuv420p \
        "$output_h265" \
        -loglevel error -stats 2>/dev/null; then
        
        local end_time=$(date +%s)
        local encoding_time=$((end_time - start_time))
        local compressed_size=$(get_size_mb "$output_h265")
        local compression_ratio=$(echo "scale=2; $original_size / $compressed_size" | bc)
        local size_reduction=$(echo "scale=1; (1 - $compressed_size / $original_size) * 100" | bc)
        
        print_success "Compressed: ${compressed_size} MB (${compression_ratio}:1 ratio, ${size_reduction}% reduction)"
        print_success "Encoding time: ${encoding_time}s"
        
        # Log results
        echo "$video_name|$original_size|$compressed_size|$compression_ratio|$size_reduction|$encoding_time|$duration|$resolution" >> "$BATCH_OUTPUT_DIR/results.csv"
    else
        print_error "Compression failed for $video_name"
        echo "$video_name|$original_size|FAILED|0|0|0|$duration|$resolution" >> "$BATCH_OUTPUT_DIR/results.csv"
    fi
    
    echo ""
}

# Generate batch summary report
generate_batch_summary() {
    print_header "Generating Batch Summary Report"
    
    cat > "$SUMMARY_FILE" << EOF
# 📦 Batch Video Compression Summary

**Test Date**: $(date)  
**Compression Method**: H.265 CRF 14 (Recommended for Cloudinary)  
**Target Resolution**: 2560x1440  

## 📊 Results Overview

| Video | Original (MB) | Compressed (MB) | Ratio | Reduction | Time (s) | Duration | Resolution |
|-------|---------------|-----------------|-------|-----------|----------|----------|------------|
EOF
    
    if [[ -f "$BATCH_OUTPUT_DIR/results.csv" ]]; then
        while IFS='|' read -r video original compressed ratio reduction time duration resolution; do
            if [[ "$compressed" != "FAILED" ]]; then
                echo "| $video | $original | $compressed | ${ratio}:1 | ${reduction}% | ${time}s | ${duration}s | $resolution |" >> "$SUMMARY_FILE"
            else
                echo "| $video | $original | FAILED | - | - | - | ${duration}s | $resolution |" >> "$SUMMARY_FILE"
            fi
        done < "$BATCH_OUTPUT_DIR/results.csv"
    fi
    
    cat >> "$SUMMARY_FILE" << EOF

## 📈 Statistics

EOF
    
    if [[ -f "$BATCH_OUTPUT_DIR/results.csv" ]]; then
        local total_original=0
        local total_compressed=0
        local successful_count=0
        local failed_count=0
        local total_time=0
        
        while IFS='|' read -r video original compressed ratio reduction time duration resolution; do
            if [[ "$compressed" != "FAILED" ]]; then
                total_original=$(echo "$total_original + $original" | bc)
                total_compressed=$(echo "$total_compressed + $compressed" | bc)
                total_time=$(echo "$total_time + $time" | bc)
                ((successful_count++))
            else
                ((failed_count++))
            fi
        done < "$BATCH_OUTPUT_DIR/results.csv"
        
        if [[ $successful_count -gt 0 ]]; then
            local overall_ratio=$(echo "scale=2; $total_original / $total_compressed" | bc)
            local overall_reduction=$(echo "scale=1; (1 - $total_compressed / $total_original) * 100" | bc)
            local avg_time=$(echo "scale=1; $total_time / $successful_count" | bc)
            
            cat >> "$SUMMARY_FILE" << EOF
- **Total Original Size**: ${total_original} MB
- **Total Compressed Size**: ${total_compressed} MB
- **Overall Compression Ratio**: ${overall_ratio}:1
- **Overall Size Reduction**: ${overall_reduction}%
- **Average Encoding Time**: ${avg_time}s per video
- **Successful Compressions**: $successful_count
- **Failed Compressions**: $failed_count

## 🎯 Recommendations

### For Cloudinary Upload:
- **Method**: H.265 CRF 14 provides excellent quality with good compression
- **Average file size reduction**: ${overall_reduction}%
- **Browser compatibility**: Modern browsers (Chrome 70+, Firefox 65+, Safari 13+)

### Alternative Options:
- If files are still too large, consider H.265 CRF 16 for smaller sizes
- For maximum compatibility, use H.264 CRF 18 (larger files but universal support)

### Quality Assessment:
- CRF 14 provides near-lossless quality suitable for professional use
- Recommended for high-quality video content on Cloudinary
- Good balance between file size and visual quality

---
*Generated by Batch Video Compression Test Suite*
EOF
        fi
    fi
    
    print_success "Summary report generated: $SUMMARY_FILE"
}

# Find all video files
find_video_files() {
    find . -maxdepth 2 -name "*.mp4" -o -name "*.MP4" -o -name "*.mov" -o -name "*.MOV" | grep -v "compression" | sort
}

# Main function
main() {
    print_header "🎬 BATCH VIDEO COMPRESSION TEST"
    
    # Check dependencies
    if ! command -v ffmpeg >/dev/null 2>&1; then
        print_error "ffmpeg not found. Please install ffmpeg."
        exit 1
    fi
    
    if ! command -v bc >/dev/null 2>&1; then
        print_error "bc not found. Please install bc calculator."
        exit 1
    fi
    
    # Create batch output directory
    mkdir -p "$BATCH_OUTPUT_DIR"
    
    # Initialize results file
    echo "Video|Original|Compressed|Ratio|Reduction|Time|Duration|Resolution" > "$BATCH_OUTPUT_DIR/results.csv"
    
    # Find all video files
    local video_files=($(find_video_files))
    
    if [[ ${#video_files[@]} -eq 0 ]]; then
        print_warning "No video files found in current directory and subdirectories"
        exit 1
    fi
    
    print_info "Found ${#video_files[@]} video files to process"
    echo ""
    
    # Process each video
    local count=1
    for video in "${video_files[@]}"; do
        print_info "Processing $count/${#video_files[@]}: $video"
        test_single_video "$video"
        ((count++))
    done
    
    # Generate summary
    generate_batch_summary
    
    print_header "🎉 BATCH PROCESSING COMPLETE"
    print_success "Results saved in: $BATCH_OUTPUT_DIR"
    print_success "Summary report: $SUMMARY_FILE"
    
    # Show quick summary
    if [[ -f "$BATCH_OUTPUT_DIR/results.csv" ]]; then
        local successful=$(grep -v "FAILED" "$BATCH_OUTPUT_DIR/results.csv" | wc -l)
        successful=$((successful - 1)) # Subtract header
        local failed=$(grep "FAILED" "$BATCH_OUTPUT_DIR/results.csv" | wc -l)
        
        echo ""
        print_info "Summary: $successful successful, $failed failed"
        
        if [[ $successful -gt 0 ]]; then
            print_success "Check the summary report for detailed analysis and recommendations"
        fi
    fi
}

# Run main function
main "$@"
