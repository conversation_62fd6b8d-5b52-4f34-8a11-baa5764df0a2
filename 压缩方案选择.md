# 🎬 视频压缩完整方案指南 - 最大化画质保持

## 📊 压缩技术全景对比

### 🏆 编码器选择 (按画质排序)

1. **AV1** - 最新一代编码器
   - 压缩效率: H.264的50%文件大小
   - 画质: 最优
   - 兼容性: 较新浏览器支持
   - 编码时间: 极慢

2. **H.265 (HEVC)** - 推荐选择
   - 压缩效率: H.264的40-50%文件大小
   - 画质: 优秀
   - 兼容性: 现代浏览器良好支持
   - 编码时间: 慢

3. **VP9** - Google开源
   - 压缩效率: H.264的30-40%文件大小
   - 画质: 良好
   - 兼容性: Chrome/Firefox原生支持
   - 编码时间: 中等

4. **H.264 (AVC)** - 传统标准
   - 压缩效率: 基准
   - 画质: 标准
   - 兼容性: 全平台支持
   - 编码时间: 快

## 🎯 质量控制方法

### 1. CRF (恒定质量因子) - 推荐
```bash
# H.265 接近无损
-c:v libx265 -crf 12-16 -preset slower

# H.264 高质量
-c:v libx264 -crf 16-20 -preset veryslow
```

**CRF值指南**:
- **H.265**: CRF 12(无损) → CRF 28(低质量)
- **H.264**: CRF 16(无损) → CRF 32(低质量)
- **AV1**: CRF 15(无损) → CRF 35(低质量)

### 2. 两遍编码 (精确大小控制)
```bash
# 第一遍分析
-pass 1 -b:v 8000k -f null /dev/null

# 第二遍编码
-pass 2 -b:v 8000k output.mp4
```

### 3. 三遍编码 (极致优化)
```bash
# 第一遍: 快速分析
-pass 1 -preset ultrafast

# 第二遍: 详细分析  
-pass 2 -preset slow

# 第三遍: 最终编码
-pass 3 -preset slower
```

## 🔧 高级参数优化

### H.265 极致画质参数
```bash
ffmpeg -i input.mp4 \
  -c:v libx265 \
  -crf 14 \
  -preset slower \
  -profile:v main \
  -level 5.1 \
  -x265-params "me=umh:subme=3:ref=5:bframes=8:b-adapt=2:rd=3:psy-rd=1.0:psy-rdoq=1.0:aq-mode=3:aq-strength=0.8:deblock=1,1" \
  -pix_fmt yuv420p \
  -an \
  output.mp4
```

### AV1 最新技术参数
```bash
ffmpeg -i input.mp4 \
  -c:v libaom-av1 \
  -crf 18 \
  -b:v 0 \
  -cpu-used 2 \
  -row-mt 1 \
  -tiles 2x2 \
  -g 240 \
  -keyint_min 23 \
  -pix_fmt yuv420p \
  -an \
  output.mp4
```

### VP9 优化参数
```bash
ffmpeg -i input.mp4 \
  -c:v libvpx-vp9 \
  -crf 20 \
  -b:v 0 \
  -cpu-used 2 \
  -row-mt 1 \
  -tile-columns 2 \
  -frame-parallel 1 \
  -an \
  output.mp4
```

## 🎨 画质增强技术

### 1. 分辨率优化
```bash
# 保持原分辨率
-vf "scale=2560:1440:flags=lanczos"

# 智能缩放 (保持宽高比)
-vf "scale='min(2560,iw)':'min(1440,ih)':force_original_aspect_ratio=decrease"

# 高质量重采样
-vf "scale=2560:1440:flags=lanczos:param0=3"
```

### 2. 色彩空间优化
```bash
# 保持原始色彩空间
-colorspace bt709 -color_primaries bt709 -color_trc bt709

# HDR内容处理
-colorspace bt2020nc -color_primaries bt2020 -color_trc smpte2084
```

### 3. 去噪和锐化
```bash
# 轻微去噪
-vf "hqdn3d=2:1:2:1"

# 自适应锐化
-vf "unsharp=5:5:0.8:3:3:0.4"

# 组合滤镜
-vf "hqdn3d=2:1:2:1,unsharp=5:5:0.8:3:3:0.4,scale=2560:1440:flags=lanczos"
```

## 🚀 终极画质保持方案

### 方案1: H.265 接近无损 (推荐)
```bash
ffmpeg -i anniversary.mp4 \
  -c:v libx265 \
  -crf 12 \
  -preset slower \
  -profile:v main \
  -level 5.1 \
  -x265-params "me=umh:subme=3:ref=5:bframes=8:rd=3:psy-rd=1.0:aq-mode=3" \
  -vf "scale=2560:1440:flags=lanczos:param0=3" \
  -colorspace bt709 \
  -color_primaries bt709 \
  -color_trc bt709 \
  -an \
  -movflags +faststart \
  -pix_fmt yuv420p \
  anniversary_ultimate.mp4
```
**预期**: 60-80MB, 视觉无损

### 方案2: AV1 最新技术
```bash
ffmpeg -i anniversary.mp4 \
  -c:v libaom-av1 \
  -crf 15 \
  -cpu-used 1 \
  -row-mt 1 \
  -tiles 4x2 \
  -g 240 \
  -keyint_min 23 \
  -aq-mode 1 \
  -tune-content 0 \
  -vf "scale=2560:1440:flags=lanczos" \
  -an \
  anniversary_av1.mp4
```
**预期**: 40-50MB, 最优画质

### 方案3: H.265 两遍编码精确控制
```bash
# 第一遍
ffmpeg -i anniversary.mp4 \
  -c:v libx265 \
  -b:v 9000k \
  -preset slower \
  -pass 1 \
  -f null /dev/null

# 第二遍
ffmpeg -i anniversary.mp4 \
  -c:v libx265 \
  -b:v 9000k \
  -preset slower \
  -pass 2 \
  -profile:v main \
  -level 5.1 \
  -vf "scale=2560:1440:flags=lanczos" \
  -an \
  -movflags +faststart \
  anniversary_2pass.mp4
```
**预期**: 精确70MB, 优化质量分布

### 方案4: 混合编码策略
```bash
# 为不同场景使用不同参数
ffmpeg -i anniversary.mp4 \
  -c:v libx265 \
  -crf 14 \
  -preset slower \
  -x265-params "me=umh:subme=3:ref=6:bframes=8:b-adapt=2:rd=4:psy-rd=1.0:psy-rdoq=2.0:aq-mode=3:aq-strength=0.8:qcomp=0.6:deblock=1,1:sao=1:limit-sao=1" \
  -vf "hqdn3d=1:0.5:1:0.5,scale=2560:1440:flags=lanczos:param0=3" \
  -an \
  anniversary_hybrid.mp4
```
**预期**: 65-75MB, 最佳质量/大小平衡

## 📊 压缩效果预测表

| 方案 | 编码器 | CRF/码率 | 预期大小 | 画质等级 | 兼容性 |
|------|--------|----------|----------|----------|--------|
| AV1 CRF 15 | libaom-av1 | CRF 15 | 40-50MB | ⭐⭐⭐⭐⭐ | 新浏览器 |
| H.265 CRF 12 | libx265 | CRF 12 | 60-80MB | ⭐⭐⭐⭐⭐ | 现代浏览器 |
| H.265 CRF 14 | libx265 | CRF 14 | 50-65MB | ⭐⭐⭐⭐⭐ | 现代浏览器 |
| H.265 两遍 | libx265 | 9000k | 70MB | ⭐⭐⭐⭐⭐ | 现代浏览器 |
| VP9 CRF 18 | libvpx-vp9 | CRF 18 | 80-100MB | ⭐⭐⭐⭐ | Chrome/Firefox |
| H.264 CRF 16 | libx264 | CRF 16 | 120-150MB | ⭐⭐⭐⭐ | 全平台 |

## 🎯 最终推荐

**最佳画质保持方案**: **H.265 CRF 12-14**
- 画质接近无损
- 文件大小适中 (50-80MB)
- 现代浏览器良好支持
- 编码时间可接受

**未来方案**: **AV1 CRF 15**
- 最优压缩效率
- 最小文件大小 (40-50MB)
- 需要较新浏览器支持

这些方案都移除了音频轨道 (`-an`)，专注于视觉质量的最大化保持。
