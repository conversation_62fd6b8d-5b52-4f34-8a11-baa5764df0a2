#!/bin/bash

# 🎬 Simple Video Compression Test Launcher
# Simplified version for testing

set -e

# Color codes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_header() { echo -e "${PURPLE}========== $1 ==========${NC}"; }
print_menu() { echo -e "${CYAN}$1${NC}"; }

# Show banner
echo -e "${PURPLE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                🎬 Video Compression Test Suite               ║"
echo "║                                                              ║"
echo "║           Find the best compression for Cloudinary!         ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"
echo ""

# Find videos
videos=($(find . -maxdepth 2 -name "*.mp4" -o -name "*.MP4" -o -name "*.mov" -o -name "*.MOV" | grep -v compression | sort))

if [[ ${#videos[@]} -eq 0 ]]; then
    print_error "No video files found!"
    exit 1
fi

print_header "Available Videos"
echo ""

# Show videos with numbers
for i in "${!videos[@]}"; do
    video="${videos[$i]}"
    size=$(stat -c%s "$video" 2>/dev/null | awk '{print int($1/1024/1024)}' || echo "?")
    dir=$(dirname "$video" | sed 's|^\./||')
    name=$(basename "$video")

    if [[ "$dir" == "." ]]; then
        print_menu "$((i+1))) $name (${size}MB)"
    else
        print_menu "$((i+1))) $dir/$name (${size}MB)"
    fi
done

echo ""
print_menu "A) Test ALL videos (batch mode)"
echo ""

# Get user choice with explicit prompt
while true; do
    echo -e "${CYAN}Select video (1-${#videos[@]}, A for all):${NC} "
    read -r choice
    
    if [[ "$choice" =~ ^[0-9]+$ ]] && [[ $choice -ge 1 ]] && [[ $choice -le ${#videos[@]} ]]; then
        selected_video="${videos[$((choice-1))]}"
        break
    elif [[ "$choice" == "A" || "$choice" == "a" ]]; then
        print_info "Starting batch test..."
        ./batch_compress_test.sh
        exit 0
    else
        print_error "Invalid choice. Please enter a number between 1 and ${#videos[@]}, or 'A' for all."
        echo ""
    fi
done

echo ""
print_success "Selected: $selected_video"
echo ""

# Show test methods
print_header "Compression Test Methods"
echo ""
print_menu "1) 🚀 Quick Test (3 methods, ~5 minutes)"
print_menu "   - H.265 CRF 14 (Recommended)"
print_menu "   - H.265 CRF 16 (Smaller)"
print_menu "   - H.264 CRF 18 (Compatible)"
echo ""
print_menu "2) 📊 Comprehensive Test (7+ methods, ~30 minutes)"
print_menu "   - All H.265 variants"
print_menu "   - AV1 (if available)"
print_menu "   - VP9 (if available)"
print_menu "   - Two-pass encoding"
echo ""
print_menu "3) ⚡ Essential Only (2 methods, ~3 minutes)"
print_menu "   - H.265 CRF 14"
print_menu "   - H.265 CRF 16"
echo ""

# Get method choice
while true; do
    echo -e "${CYAN}Select test method (1-3):${NC} "
    read -r method_choice
    
    if [[ "$method_choice" =~ ^[1-3]$ ]]; then
        break
    else
        print_error "Invalid choice. Please enter 1, 2, or 3."
        echo ""
    fi
done

echo ""
print_header "Starting Compression Test"
print_info "Video: $selected_video"

case $method_choice in
    1)
        print_info "Running Quick Test..."
        ./quick_compress_test.sh "$selected_video"
        ;;
    2)
        print_info "Running Comprehensive Test..."
        ./video_compression_test.sh "$selected_video"
        ;;
    3)
        print_info "Running Essential Test..."
        ./video_compression_test.sh --quick "$selected_video"
        ;;
esac

echo ""
print_success "Test completed! Check the output files for results."
print_info "💡 Tip: Look for the .md report files for detailed analysis and recommendations."
